<template>
  <div class="studio-container">
    <!-- 使用步骤导航组件，放在顶部 -->
    <StepsNavigationBar
      :project-title="projectData?.title || '未命名项目'"
      :chapter-title="localChapterTitle"
      :current-step="currentStep"
      :has-srt-file="hasSrtFile"
      :has-audio-file="hasAudioFile"
      :use-builtin-audio="useBuiltinAudio"
      :show-export-button="true"
      @go-back="goBack"
      @next-step="goToNextStep"
      @update-title="updateProjectTitle"
      @navigate-to-step="navigateToStep"
      @show-error="showErrorMessage"
      @export-import="handleExportImport"
    />

    <!-- 次级导航栏 -->
    <SecondaryNavbar
      ref="secondaryNavbar"
      :drawing-count="0"
      :total-drawings="129"
      :active-tab="activeNavTab"
      :steps="['match-tags', 'settings']"
      :batch-inference-state="batchOperationUI.batchInferenceState"
      :batch-image-generation-state="batchOperationUI.batchImageGenerationState"
      @tab-change="handleTabChange"
      @action="handleNavAction"
    />

    <!-- 合并的操作栏：强制刷新按钮和选择状态信息 -->
    <div class="combined-action-bar">
      <div class="left-actions">
        <button
          class="refresh-btn"
          @click="forceRefreshSrtContent"
          title="强制刷新SRT内容"
        >
          <i class="ri-refresh-line" /> 强制刷新SRT内容
        </button>
      </div>
      <div class="right-info">
        <div class="selection-info">
          已选择 {{ selectedRowCount }} 项
        </div>
      </div>
    </div>

    <!-- SRT数据加载中或无数据时显示 -->
    <div
      v-if="uiStateStudio.uiState.isLoading"
      class="no-data-message"
    >
      <div class="message-icon">
        <i class="icon-loading" />
      </div>
      <div class="message-text">
        {{ uiStateStudio.uiState.loadingMessage }}
      </div>
    </div>

    <!-- 无数据但已加载完成时显示 -->
    <div
      v-if="!uiStateStudio.uiState.isLoading && !hasRows"
      class="no-data-message"
    >
      <div class="message-icon">
        📄
      </div>
      <div class="message-text">
        没有找到SRT字幕数据
      </div>
      <div class="message-subtext">
        请上传SRT文件或选择已有的SRT文件
      </div>
      <RouterLink
        to="/creation"
        class="back-button"
      >
        返回创建页面
      </RouterLink>
    </div>

    <!-- 使用模块化的网格布局，实现粘性表头 -->
    <div v-if="hasRows" class="table-container">
      <!-- 固定表头 -->
      <div class="table-header-container">
        <GridLayout>
          <GridHeader
            @open-image-prompt-settings="handleOpenImagePromptSettings"
            @clear-all-keywords="handleClearAllKeywords"
            @select-all="handleSelectAll"
            @clear-selection="handleClearSelection"
          />
        </GridLayout>
      </div>

      <!-- 可滚动的数据区域 -->
      <div class="table-body-container">
        <GridLayout>
          <!-- 动态生成多行数据 -->
          <GridRow
            v-for="(row, index) in rows"
            :key="index"
          >
        <SelectCell
          :is-selected="row.isSelected"
          :is-first-row="index === 0"
          :is-merged="row.isMerged"
          :is-locked="row.isLocked || false"
          :has-locked-rows="hasLockedRows"
          @toggle-select="toggleRowSelection(index)"
          @merge-up="handleMergeUp(index)"
          @split-down="handleSplitDown(index)"
          @show-locked-message="showLockedRowMessage"
        />

        <IndexCell :row-index="index" />
        <DescriptionCell :description="row.description" />

        <TagsCell
          :tags="row.tags || []"
          :selected-characters="currentSelectedCharacters"
          :row-index="index"
          :persisted-selected-tags="row.selectedTags || []"
          :is-locked="row.isLocked || false"
          @tag-selection-changed="handleTagSelectionChanged"
          @keywords-updated="handleKeywordsUpdated"
          @show-locked-message="showLockedRowMessage"
        />

        <KeywordCell
          :keywords="row.keywords"
          :row-index="index"
          :is-inferring="row.isInferring || false"
          :is-locked="row.isLocked || false"
          @keywords-updated="event => updateKeywords(event.rowIndex, event.keywords)"
          @clear-tags="handleClearTags"
          @show-locked-message="showLockedRowMessage"
        />

        <ImageCell
          :image-src="row.imageSrc"
          :image-alt="row.imageAlt"
          :is-locked="row.isLocked || false"
          @select-image="() => handleSelectMainImage(index)"
          @delete-image="() => handleDeleteMainImage(index)"
          @show-locked-message="showLockedRowMessage"
        />

        <OptionalImageCell
          :thumbnails="row.thumbnails || []"
          :is-locked="row.isLocked || false"
          @select-thumbnail="thumbnailIndex => handleSelectThumbnail(index, thumbnailIndex)"
          @manage-images="() => handleManageImages(index)"
          @delete-thumbnail="thumbnailIndex => handleDeleteThumbnail(index, thumbnailIndex)"
          @show-locked-message="showLockedRowMessage"
        />

        <OperationCell
          :is-generating="row.isGenerating || false"
          :generation-progress="row.generationProgress || 0"
          :generation-message="row.generationMessage || ''"
          :can-generate="true"
          :has-keywords="!!(row.keywords && row.keywords.trim())"
          :is-inferring="row.isInferring || false"
          :is-locked="row.isLocked || false"
          :is-queued="row.isQueued || false"
          :queue-position="row.queuePosition || 0"
          :queue-task-id="row.queueTaskId || ''"
          @redraw-image="() => handleRedrawImage(index)"
          @infer-prompt="() => handleInferPrompt(index)"
          @preview-prompt="() => handlePreviewPrompt(index)"
          @generate-image="() => handleGenerateImage(index)"
          @cancel-generation="() => handleCancelGeneration(index)"
          @cancel-queue="() => handleCancelQueue(index)"
          @toggle-lock="() => handleToggleRowLock(index)"
        />
          </GridRow>
        </GridLayout>
      </div>
    </div>

    <!-- 添加Toast通知组件 - 使用composable状态 -->
    <ToastNotification
      ref="toast"
      :key="toastNotification.toastUpdateFlag"
      :message="toastNotification.toastMessage"
      :title="toastNotification.toastTitle"
      :type="toastNotification.toastType"
      :duration="5000"
    />

    <!-- 🆕 队列状态显示 - 改进版本 -->
    <div
      v-if="shouldShowQueuePanel"
      class="queue-status-panel"
      :class="{ 'collapsed': uiStateStudio.uiState.isQueuePanelCollapsed }"
    >
      <!-- 面板头部 - 可点击折叠/展开 -->
      <div class="queue-header" @click="uiStateStudio.toggleQueuePanel">
        <i class="ri-time-line queue-icon" />
        <span class="queue-title">图像生成队列</span>
        <i
          class="ri-arrow-up-s-line collapse-icon"
          :class="{ 'rotated': uiStateStudio.uiState.isQueuePanelCollapsed }"
        />
      </div>

      <!-- 面板内容 - 可折叠 -->
      <div v-if="!uiStateStudio.uiState.isQueuePanelCollapsed" class="queue-content">
        <!-- 🆕 队列状态显示 - 始终可见 -->
        <div class="queue-info">
          <div class="queue-stat">
            <span class="stat-label">正在生成:</span>
            <span class="stat-value">{{ queueProcessingCount }}</span>
          </div>
          <div class="queue-stat">
            <span class="stat-label">等待中:</span>
            <span class="stat-value">{{ imageGeneration.queueStats.waiting }}</span>
          </div>
          <div class="queue-stat">
            <span class="stat-label">总计:</span>
            <span class="stat-value">{{ imageGeneration.queueStats.total }}</span>
          </div>
        </div>

        <!-- 🆕 空队列状态提示 -->
        <div
          v-if="queueProcessingCount === 0 && imageGeneration.queueStats.waiting === 0"
          class="queue-empty-state"
        >
          <i class="ri-checkbox-circle-line empty-icon" />
          <span class="empty-text">暂无排队任务</span>
        </div>

        <!-- 队列操作按钮 -->
        <div
          v-if="imageGeneration.queueStats.waiting > 0"
          class="queue-actions"
        >
          <button
            class="queue-action-btn clear-queue"
            @click="handleClearQueue"
            title="清空所有等待中的任务"
          >
            <i class="ri-delete-bin-line" />
            清空队列
          </button>
        </div>
      </div>

      <!-- 最小化时的简化显示 -->
      <div v-else class="queue-summary">
        <span class="summary-text">
          <!-- 🆕 改进的队列状态显示逻辑 -->
          <template v-if="queueProcessingCount > 0">
            生成中
          </template>
          <template v-else-if="imageGeneration.queueStats.waiting > 0">
            等待{{ imageGeneration.queueStats.waiting }}个
          </template>
          <template v-else>
            空闲
          </template>
        </span>
      </div>
    </div>



    <!-- 图像提示词测试窗口 -->
    <ImagePromptTestModal
      :show="uiStateStudio.uiState.showImagePromptTestModal"
      :prompt-data="uiStateStudio.uiState.currentPromptData"
      :full-prompt="uiStateStudio.uiState.fullPromptPreview"
      :is-loading="uiStateStudio.uiState.isImagePromptLoading"
      :available-characters="availableCharactersForPrompt"
      @update:show="uiStateStudio.uiState.showImagePromptTestModal = $event"
      @confirm-send="handleConfirmSendPrompt"
      @close="handleCloseTestModal"
      @apply-character-tags="handleApplyCharacterTags"
    />

    <!-- ComfyUI错误恢复模态框已移除，改用toast通知 -->

    <!-- 图像提示词设置弹窗 -->
    <ImagePromptSettingsModal
      :show="uiStateStudio.uiState.showImagePromptDrawer"
      @update:show="uiStateStudio.uiState.showImagePromptDrawer = $event"
      :project-title="localProjectTitle"
      :chapter-title="localChapterTitle"
      :available-characters="availableCharactersForPrompt"
    />

    <!-- 图片管理窗口 -->
    <ImageManagementModal
      :visible="imageManagement.showImageManagementModal.value"
      :row-index="imageManagement.currentManageImageRowIndex.value"
      :main-image="imageManagement.getCurrentManageImageData(rows).mainImage"
      :optional-images="imageManagement.getCurrentManageImageData(rows).optionalImages"
      :is-locked="imageManagement.getCurrentManageImageData(rows).isLocked"
      @close="closeImageManagementModal"
      @delete-main-image="handleDeleteMainImageFromModal"
      @delete-optional-image="handleDeleteOptionalImageFromModal"
      @upload-images="handleUploadImagesFromModal"
      @upload-error="handleUploadErrorFromModal"
      @replace-main-image="handleReplaceMainImageFromModal"
      @replace-optional-image="handleReplaceOptionalImageFromModal"
    />
  </div>
</template>

<script>
import StepsNavigationBar from './StepsNavigationBar.vue';
import SecondaryNavbar from './SecondaryNavbar.vue';
import ToastNotification from './ToastNotification.vue';
import ImagePromptSettingsModal from './ImagePromptSettingsModal.vue';

// 导入网格布局组件
import GridLayout from './grid/GridLayout.vue';
import GridHeader from './grid/GridHeader.vue';
import GridRow from './grid/GridRow.vue';

// 导入单元格组件
import SelectCell from './cells/SelectCell.vue';
import IndexCell from './cells/IndexCell.vue';
import DescriptionCell from './cells/DescriptionCell.vue';
import TagsCell from './cells/TagsCell.vue';
import KeywordCell from './cells/KeywordCell.vue';
import ImageCell from './cells/ImageCell.vue';
import OptionalImageCell from './cells/OptionalImageCell.vue';
import OperationCell from './cells/OperationCell.vue';

// 导入业务逻辑
import { useStudioActions } from '../composables/useStudioActions';
import { provideProjectContext } from '../composables/useProjectContext.js';
import { useImagePromptReasoning } from '../composables/useImagePromptReasoning.js';
import { useImageGeneration } from '../composables/useImageGeneration.js';
import { useBatchOperationUI } from '../composables/useBatchOperationUI.js';
import { useBatchInferenceSequential } from '../composables/useBatchInferenceSequential.js';
import { useToastNotification } from '../composables/useToastNotification.js';
import { useImageManagement } from '../composables/useImageManagement.js';
import { useRowOperations } from '../composables/useRowOperations.js';
import { useDataPersistenceStudio } from '../composables/useDataPersistenceStudio.js';
import { useProjectContextStudio } from '../composables/useProjectContextStudio.js';
import { useBatchOperationsStudio } from '../composables/useBatchOperationsStudio.js';
import { usePromptReasoningStudio } from '../composables/usePromptReasoningStudio.js';
import { useImageGenerationStudio } from '../composables/useImageGenerationStudio.js';
import { useDataManagementStudio } from '../composables/useDataManagementStudio.js';
import { useUIStateStudio } from '../composables/useUIStateStudio.js';
import { useLifecycleStudio } from '../composables/useLifecycleStudio.js';
import { dataValidationUtils } from '../utils/dataValidation.js';
import { debugUtils } from '../utils/debugTools.js';

// 导入测试窗口组件
import ImagePromptTestModal from './ImagePromptTestModal.vue';

// 导入图片管理窗口组件
import ImageManagementModal from './common/ImageManagementModal.vue';




export default {
  name: 'ContentCreationStudio',
  components: {
    StepsNavigationBar,
    SecondaryNavbar,
    ToastNotification,
    ImagePromptSettingsModal,
    ImagePromptTestModal,
    ImageManagementModal,
    GridLayout,
    GridHeader,
    GridRow,
    SelectCell,
    IndexCell,
    DescriptionCell,
    TagsCell,
    KeywordCell,
    ImageCell,
    OptionalImageCell,
    OperationCell,
  },
  emits: ['navigate', 'open-adjust-shots-drawer', 'open-global-reasoning-drawer', 'close-global-reasoning-drawer'],
  props: {
    project: {
      type: Object,
      required: false,
      default: () => null
    },
    projectTitle: {
      type: String,
      required: false,
      default: null
    },
    chapterTitle: {
      type: String,
      required: false,
      default: null
    }
  },
  setup() {
    // 提供项目上下文
    const projectContextTools = provideProjectContext();

    // 使用抽取的业务逻辑，但排除已弃用的saveProject方法
    const studioActions = useStudioActions();

    // 直接使用studioActions，saveProject方法已从useStudioActions中完全移除
    const safeStudioActions = studioActions;

    // 初始化图像提示词推理功能
    const imagePromptReasoning = useImagePromptReasoning();

    // 初始化图像生成功能
    const imageGeneration = useImageGeneration();

    // 🆕 初始化批量操作UI管理
    const batchOperationUI = useBatchOperationUI();

    // 🆕 初始化顺序批量推理
    const batchInferenceSequential = useBatchInferenceSequential();

    // 🆕 初始化Toast通知系统
    const toastNotification = useToastNotification();

    // 🆕 初始化图片管理系统
    const imageManagement = useImageManagement();

    // 🆕 初始化行操作系统
    const rowOperations = useRowOperations();

    // 🆕 初始化数据持久化系统
    const dataPersistence = useDataPersistenceStudio();

    // 🆕 初始化项目上下文管理系统
    const projectContextStudio = useProjectContextStudio();

    // 🆕 初始化批量操作管理系统
    const batchOperationsStudio = useBatchOperationsStudio();

    // 🆕 初始化提示词推理管理系统
    const promptReasoningStudio = usePromptReasoningStudio();

    // 🆕 初始化图像生成管理系统
    const imageGenerationStudio = useImageGenerationStudio();

    // 🔧 暴露到window对象以便调试和外部访问
    window.useImageGenerationStudio = imageGenerationStudio;

    // 🆕 初始化数据管理系统
    const dataManagementStudio = useDataManagementStudio();

    // 🔧 设置ComfyUI回调的方法 - 使用正确的this绑定
    const setupComfyUICallback = () => {
      if (!window.comfyuiImageGeneration) {
        console.log('⏳ [ComfyUI回调] ComfyUI客户端尚未初始化，稍后重试');
        return false;
      }

      if (!window.comfyuiImageGeneration.callbacks) {
        window.comfyuiImageGeneration.callbacks = {};
      }

      // 🔧 保存组件实例的引用，避免this绑定问题
      const componentInstance = this;

      window.comfyuiImageGeneration.callbacks.onImageGenerated = (rowIndex, imageData) => {
        console.log('🎨 [ComfyUI回调] 图像生成完成:', { rowIndex, imageData });

        // 🔧 使用保存的组件实例引用
        const currentRows = componentInstance.rows;
        const currentProjectData = componentInstance.projectData;

        if (!currentRows || currentRows.length === 0) {
          console.error('❌ [ComfyUI回调] 当前没有行数据');
          console.log('🔧 [ComfyUI回调] 调试信息:', {
            hasComponentInstance: !!componentInstance,
            hasRows: !!componentInstance.rows,
            rowsLength: componentInstance.rows ? componentInstance.rows.length : 'undefined',
            hasProjectData: !!componentInstance.projectData
          });
          return;
        }

        console.log('🔧 [ComfyUI回调] 项目数据状态:', {
          hasRows: !!currentRows,
          rowsLength: currentRows.length,
          projectTitle: componentInstance.localProjectTitle,
          chapterTitle: componentInstance.localChapterTitle
        });

        // 🔧 创建完整的组件上下文
        const componentContext = {
          rows: currentRows,
          localProjectTitle: componentInstance.localProjectTitle,
          localChapterTitle: componentInstance.localChapterTitle,
          projectData: currentProjectData,
          saveProjectDataImmediately: dataManagementStudio.saveProjectDataImmediately,
          debouncedSaveProject: dataManagementStudio.debouncedSaveProject,
          $forceUpdate: () => componentInstance.$forceUpdate()
        };

        // 直接调用已有的图像处理方法
        imageGenerationStudio.handleSingleImageGenerated(componentContext, rowIndex, imageData);
      };

      console.log('✅ ComfyUI图像生成回调已设置');
      return true;
    };

    // 🔧 立即尝试设置回调，如果失败则稍后重试
    if (!setupComfyUICallback()) {
      // 如果ComfyUI客户端还没初始化，等待一下再重试
      setTimeout(() => {
        setupComfyUICallback();
      }, 1000);
    }

    // 🆕 初始化UI状态管理系统
    const uiStateStudio = useUIStateStudio();

    // 🆕 初始化生命周期管理系统
    const lifecycleStudio = useLifecycleStudio();

    // 返回项目上下文工具和安全的studio actions（不包含弃用方法）
    return {
      ...safeStudioActions,
      projectContextTools,
      imagePromptReasoning,
      imageGeneration,
      batchOperationUI,
      batchInferenceSequential,
      toastNotification,
      imageManagement,
      rowOperations,
      dataPersistence,
      projectContextStudio,
      batchOperationsStudio,
      promptReasoningStudio,
      imageGenerationStudio,
      dataManagementStudio,
      uiStateStudio,
      lifecycleStudio,
      dataValidationUtils,
      debugUtils
    };
  },
  data() {
    return {
      // 项目数据
      localProjectTitle: '',
      projectData: {},
      localChapterTitle: '',  // 添加章节标题
      // 步骤导航相关数据
      currentStep: 'edit',  // 默认为编辑模式
      // SecondaryNavbar相关数据
      activeNavTab: 'match-tags',
      // StepsNavigationBar所需数据
      hasSrtFile: false,
      hasAudioFile: false,
      useBuiltinAudio: false,

      // 行数据数组
      rows: [],

      // 可用角色列表
      availableCharacters: [],

      // 预设角色列表
      presetCharacters: [],

      // 当前分析结果
      currentAnalysisResults: null,

      // 当前在全局推理抽屉中选中的角色
      currentSelectedCharacters: [],

      // 🆕 UI状态现在由 useUIStateStudio composable 管理

      // 服务器基础URL
      serverBaseUrl: 'http://localhost:8091',

      // 默认空行模板
      emptyRow: {
        isSelected: false,
        description: '',
        keywords: '',
        imageSrc: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
        imageAlt: '',
        isImageLocked: false,
        thumbnails: []
      },

      // Toast通知相关数据现在由 useToastNotification composable 管理

      // 🆕 图像提示词和队列状态相关数据已迁移到 useUIStateStudio composable

      // 🆕 图片管理窗口相关数据已迁移到 useImageManagement composable
    }
  },
  computed: {
    // 判断是否有行数据
    hasRows() {
      return this.rows.length > 0;
    },

    // 获取可用于图像提示词识别的角色列表
    availableCharactersForPrompt() {
      const characters = [];

      // 从预设角色中获取
      if (this.presetCharacters && Array.isArray(this.presetCharacters)) {
        this.presetCharacters.forEach(char => {
          characters.push({
            name: char.name,
            description: char.description,
            aliases: char.aliases || [],
            source: 'preset'
          });
        });
      }

      // 从当前分析结果中获取角色
      if (this.currentAnalysisResults && this.currentAnalysisResults.characters) {
        this.currentAnalysisResults.characters.forEach(char => {
          // 避免重复添加
          if (!characters.find(existing => existing.name === char)) {
            characters.push({
              name: char,
              description: '',
              aliases: [],
              source: 'analysis'
            });
          }
        });
      }

      // 从行数据中获取已选中的角色标签
      if (this.rows && Array.isArray(this.rows)) {
        this.rows.forEach(row => {
          if (row.selectedTags && Array.isArray(row.selectedTags)) {
            row.selectedTags.forEach(tag => {
              // 避免重复添加
              if (!characters.find(existing => existing.name === tag)) {
                characters.push({
                  name: tag,
                  description: '',
                  aliases: [],
                  source: 'tags'
                });
              }
            });
          }
        });
      }

      // console.log('[CharacterRecognition] 🎭 可用角色列表:', {
      //   totalCount: characters.length,
      //   characters: characters,
      //   presetCharactersCount: this.presetCharacters ? this.presetCharacters.length : 0,
      //   presetCharacters: this.presetCharacters,
      //   currentAnalysisResultsCharacters: this.currentAnalysisResults ? this.currentAnalysisResults.characters : null,
      //   currentAnalysisResults: this.currentAnalysisResults,
      //   rowsWithTags: this.rows ? this.rows.filter(row => row.selectedTags && row.selectedTags.length > 0).length : 0
      // });
      return characters;
    },

    // 计算选中的行数 - 使用新的UI状态管理系统
    selectedRowCount() {
      const count = this.rows.filter(row => row.isSelected).length;
      // 同步到UI状态管理
      this.uiStateStudio.updateSelectionState(this.rows);
      return count;
    },

    // 获取选中的行
    selectedRows() {
      return this.rows.filter(row => row.isSelected);
    },

    // 🆕 图像生成按钮始终可点击，检查逻辑移到点击处理中
    canGenerateImage() {
      return true;
    },

    // 🆕 检查是否有锁定的行
    hasLockedRows() {
      return this.rows.some(row => row.isLocked);
    },

    // 🆕 当前管理的图片数据已迁移到 useImageManagement composable

    // 队列相关计算属性
    shouldShowQueuePanel() {
      // 🆕 始终显示队列面板，让用户了解队列状态
      if (!this.imageGeneration) {
        return false;
      }

      // 只要图像生成服务已初始化，就显示队列面板
      return true;
    },

    queueProcessingCount() {
      if (!this.imageGeneration) {
        return 0;
      }

      // 简化逻辑，避免复杂的状态检测
      const isGenerating = this.imageGeneration.isGenerating?.value ?? this.imageGeneration.isGenerating;
      const currentTask = this.imageGeneration.currentTask?.value ?? this.imageGeneration.currentTask;
      const hasCurrentTask = currentTask && currentTask.status === 'processing';

      // 直接返回计算结果，不进行任何状态修复或日志记录
      return (isGenerating || hasCurrentTask) ? 1 : 0;
    },



  },
  async created() {
    // 🆕 使用新的生命周期管理系统
    await this.lifecycleStudio.handleCreated(this);
  },
  async mounted() {
    // 🆕 使用新的生命周期管理系统
    await this.lifecycleStudio.handleMounted(this);
  },
  beforeUnmount() {
    // 🆕 使用新的生命周期管理系统
    this.lifecycleStudio.handleBeforeUnmount(this);
  },
  methods: {
    // 🆕 检测是否存在遗留的临时状态
    hasLegacyTemporaryStates() {
      if (!this.rows || !Array.isArray(this.rows)) {
        return false;
      }

      // 检查是否有任何临时状态字段
      const temporaryFields = [
        'isQueued', 'queueTaskId', 'queuePosition', 'generationMessage',
        'generationProgress', 'generationStage', 'generationError',
        'currentTaskId', 'isGenerating', 'isInferring', 'isProcessing'
      ];

      return this.rows.some(row => {
        return temporaryFields.some(field => {
          const value = row[field];
          // 检查是否有非默认值的临时状态
          if (field === 'isQueued' || field === 'isGenerating' || field === 'isInferring' || field === 'isProcessing') {
            return value === true;
          }
          if (field === 'queuePosition' || field === 'generationProgress') {
            return value > 0;
          }
          if (field === 'queueTaskId' || field === 'generationMessage' || field === 'generationStage' || field === 'generationError' || field === 'currentTaskId') {
            return value && value !== '';
          }
          return false;
        });
      });
    },

    // 🆕 清理页面加载时的错误排队状态
    cleanLoadedQueueState() {
      console.log('🧹 [状态清理] 清理页面加载时的排队状态...');

      let cleanedCount = 0;

      if (this.rows && Array.isArray(this.rows)) {
        this.rows.forEach((row, index) => {
          let rowCleaned = false;

          // 清理排队相关状态
          if (row.isQueued === true) {
            row.isQueued = false;
            rowCleaned = true;
          }
          if (row.queueTaskId) {
            row.queueTaskId = '';
            rowCleaned = true;
          }
          if (row.queuePosition > 0) {
            row.queuePosition = 0;
            rowCleaned = true;
          }
          if (row.generationMessage &&
              (row.generationMessage.includes('队列') ||
               row.generationMessage.includes('排队'))) {
            row.generationMessage = '';
            rowCleaned = true;
          }

          if (rowCleaned) {
            cleanedCount++;
            console.log(`🧹 [状态清理] 清理第${index + 1}行的排队状态`);
          }
        });
      }

      if (cleanedCount > 0) {
        console.log(`🧹 [状态清理] 共清理了${cleanedCount}行的错误排队状态`);
        // 立即保存清理后的数据
        this.saveProjectDataImmediately();
      } else {
        console.log('🧹 [状态清理] 未发现需要清理的排队状态');
      }
    },

    // 🔧 强制重置队列状态 - 解决持久化状态问题
    forceResetQueueState() {
      console.log('🔧 [强制重置] 开始重置队列状态...');

      if (!this.imageGeneration) {
        console.warn('🔧 [强制重置] imageGeneration 未初始化');
        return;
      }

      // 🔧 直接清理后端服务状态
      try {
        if (this.imageGeneration.comfyuiImageGeneration) {
          // 强制清理所有任务
          this.imageGeneration.comfyuiImageGeneration.taskLifecycle.activeTasks.clear();
          // 重置生成状态
          this.imageGeneration.comfyuiImageGeneration.isGenerating = false;
          this.imageGeneration.comfyuiImageGeneration.currentTaskId = null;
          console.log('🔧 [强制重置] 已清理后端服务状态');
        }
      } catch (error) {
        console.error('🔧 [强制重置] 清理后端服务状态失败:', error);
      }

      // 1. 强制重置 isGenerating 状态
      try {
        if (this.imageGeneration.isGenerating) {
          if (typeof this.imageGeneration.isGenerating === 'object' && this.imageGeneration.isGenerating.value !== undefined) {
            // Vue 3 ref 对象
            this.imageGeneration.isGenerating.value = false;
            console.log('🔧 [强制重置] 重置 isGenerating.value = false');
          } else {
            // 普通属性
            this.imageGeneration.isGenerating = false;
            console.log('🔧 [强制重置] 重置 isGenerating = false');
          }
        }
      } catch (error) {
        console.error('🔧 [强制重置] 重置 isGenerating 失败:', error);
      }

      // 2. 强制重置 currentTask 状态
      try {
        if (this.imageGeneration.currentTask) {
          if (typeof this.imageGeneration.currentTask === 'object' && this.imageGeneration.currentTask.value !== undefined) {
            // Vue 3 ref 对象
            this.imageGeneration.currentTask.value = null;
            console.log('🔧 [强制重置] 重置 currentTask.value = null');
          } else {
            // 普通属性
            this.imageGeneration.currentTask = null;
            console.log('🔧 [强制重置] 重置 currentTask = null');
          }
        }
      } catch (error) {
        console.error('🔧 [强制重置] 重置 currentTask 失败:', error);
      }

      // 3. 重置队列统计
      try {
        if (this.imageGeneration.queueStats) {
          this.imageGeneration.queueStats.waiting = 0;
          this.imageGeneration.queueStats.processing = 0;
          this.imageGeneration.queueStats.total = 0;
          console.log('🔧 [强制重置] 重置队列统计');
        }
      } catch (error) {
        console.error('🔧 [强制重置] 重置队列统计失败:', error);
      }

      // 4. 调用内置的强制重置方法
      try {
        if (this.imageGeneration.forceResetState) {
          this.imageGeneration.forceResetState();
          console.log('🔧 [强制重置] 调用内置 forceResetState');
        }
      } catch (error) {
        console.error('🔧 [强制重置] 调用内置重置失败:', error);
      }

      // 5. 验证重置结果
      setTimeout(() => {
        const isGenerating = this.imageGeneration.isGenerating?.value ?? this.imageGeneration.isGenerating;
        const currentTask = this.imageGeneration.currentTask?.value ?? this.imageGeneration.currentTask;
        const queueCount = this.queueProcessingCount;
        const shouldShow = this.shouldShowQueuePanel;

        console.log('🔧 [强制重置] 重置结果验证:', {
          isGenerating,
          currentTask,
          queueProcessingCount: queueCount,
          shouldShowQueuePanel: shouldShow
        });

        // 🔧 修复验证逻辑：正确检查 currentTask 的值而不是对象本身
        const hasCurrentTask = currentTask && (currentTask.status === 'processing' || currentTask.id);

        if (isGenerating || hasCurrentTask || queueCount > 0) {
          console.error('🔧 [强制重置] 重置失败，状态仍然异常!', {
            isGenerating,
            hasCurrentTask,
            currentTaskValue: currentTask,
            queueCount
          });
        } else {
          console.log('🔧 [强制重置] 重置成功，状态正常');
        }
      }, 100);
    },

    // 🔧 修复虚假的生成状态
    fixPhantomGeneratingState() {
      console.log('🔧 [状态修复] 开始修复虚假的生成状态...');

      if (!this.imageGeneration) {
        console.warn('🔧 [状态修复] imageGeneration 未初始化');
        return;
      }

      try {
        // 修复 isGenerating 状态
        if (this.imageGeneration.isGenerating?.value !== undefined) {
          this.imageGeneration.isGenerating.value = false;
        } else {
          this.imageGeneration.isGenerating = false;
        }
        console.log('🔧 [状态修复] 已重置 isGenerating = false');

        // 确保 currentTask 也被清空
        if (this.imageGeneration.currentTask?.value !== undefined) {
          this.imageGeneration.currentTask.value = null;
        } else {
          this.imageGeneration.currentTask = null;
        }
        console.log('🔧 [状态修复] 已清空 currentTask');

        // 更新队列统计
        if (this.imageGeneration.updateQueueStats) {
          this.imageGeneration.updateQueueStats();
        }

        // 验证修复结果
        setTimeout(() => {
          const isGenerating = this.imageGeneration.isGenerating?.value ?? this.imageGeneration.isGenerating;
          const currentTask = this.imageGeneration.currentTask?.value ?? this.imageGeneration.currentTask;
          const queueCount = this.queueProcessingCount;

          console.log('🔧 [状态修复] 修复结果:', {
            isGenerating,
            currentTask,
            queueProcessingCount: queueCount
          });

          // 🔧 修复验证逻辑：正确检查 currentTask 的值
          const hasCurrentTask = currentTask && (currentTask.status === 'processing' || currentTask.id);

          if (!isGenerating && !hasCurrentTask && queueCount === 0) {
            console.log('🔧 [状态修复] 修复成功');
          } else {
            console.error('🔧 [状态修复] 修复失败，状态仍然异常', {
              isGenerating,
              hasCurrentTask,
              currentTaskValue: currentTask,
              queueCount
            });
          }
        }, 100);

      } catch (error) {
        console.error('🔧 [状态修复] 修复过程中发生错误:', error);
      }
    },

    // 🔧 启动虚假状态监控 - 暂时禁用以解决无限循环
    startPhantomStateMonitoring() {
      console.log('🔧 [状态监控] 虚假状态监控已禁用（避免无限循环）');
      // 暂时禁用定时器监控
      // this.phantomStateCheckInterval = setInterval(() => {
      //   // 监控逻辑已禁用
      // }, 5000);
    },

    // 🔧 停止虚假状态监控
    stopPhantomStateMonitoring() {
      if (this.phantomStateCheckInterval) {
        clearInterval(this.phantomStateCheckInterval);
        this.phantomStateCheckInterval = null;
        console.log('🔧 [状态监控] 已停止虚假状态监控');
      }
    },

    // 🆕 完整的图像生成状态重置
    completeImageGenerationStateReset() {
      console.log('🔧 [完整重置] 开始完整的图像生成状态重置...');

      try {
        // 1. 使用新的完整状态重置方法
        if (this.imageGeneration && this.imageGeneration.completeStateReset) {
          console.log('🔧 [完整重置] 使用完整状态重置方法');
          this.imageGeneration.completeStateReset(this.rows);
        } else {
          console.log('🔧 [完整重置] 使用回退重置方法');

          // 回退方法：手动重置各个状态
          if (this.imageGeneration) {
            // 重置全局状态
            if (this.imageGeneration.forceResetState) {
              this.imageGeneration.forceResetState();
            }

            // 手动重置行级状态
            this.rows.forEach((row) => {
              if (row) {
                row.isGenerating = false;
                row.isQueued = false;
                row.generationProgress = 0;
                row.generationMessage = '';

                if (row.queueTaskId) {
                  row.queueTaskId = '';
                  row.queuePosition = 0;
                }
              }
            });
          }
        }

        // 2. 强制更新UI
        this.$forceUpdate();

        console.log('✅ [完整重置] 完整的图像生成状态重置完成');
        return true;
      } catch (error) {
        console.error('❌ [完整重置] 完整状态重置失败:', error);
        return false;
      }
    },

    // 🆕 生命周期相关方法已迁移到 useLifecycleStudio composable

    // 🆕 性能优化：批量更新控制方法 - 使用新的UI状态管理系统
    startBatchUpdate() {
      this.uiStateStudio.startBatchUpdate();
    },

    endBatchUpdate() {
      this.uiStateStudio.endBatchUpdate(() => this.debouncedSaveProject());
    },

    // 🆕 性能优化：使用新的数据持久化系统
    async debouncedSaveProject() {
      await this.dataPersistence.debouncedSaveProject(this);
    },

    // 🆕 添加初始化项目信息的方法 - 使用新的项目上下文管理系统
    async initializeProjectInfo() {
      const projectInfo = await this.projectContextStudio.initializeProjectInfo(this, this.$props);
      const { projectTitle: projectFromProps, chapterTitle: chapterFromProps, hasMerged } = projectInfo;

      // 初始化项目数据
      if (this.project) {
        // 如果通过props传递了项目数据，优先使用它
        this.localProjectTitle = this.project.title || '未命名项目';
        this.projectData = this.project;
        // console.log('从props初始化项目数据:', this.projectData);
      } else if (projectFromProps && chapterFromProps) {
        // 如果有项目和章节信息，尝试从文件系统加载
        console.log('从props或URL加载项目数据:', projectFromProps, chapterFromProps);
        this.localProjectTitle = projectFromProps;
        this.localChapterTitle = chapterFromProps;

        // 如果有合并操作的标志，直接从project-data.json文件中加载数据
        if (hasMerged) {
          console.log('检测到合并操作标志，直接从project-data.json文件中加载数据');
          const success = await this.loadProjectDataFromFile(projectFromProps, chapterFromProps);
          if (success) {
            console.log('成功从project-data.json文件中加载数据，包含合并状态');
            return;
          } else {
            console.warn('无法从project-data.json文件中加载数据，尝试常规加载');
          }
        }
      } else {
        console.warn('没有找到项目数据，无法初始化');
        this.uiStateStudio.setLoadingState(false);
        return;
      }

      // 设置文件状态
      if (this.projectData && this.projectData.data) {
        this.hasSrtFile = !!this.projectData.data.srtFilePath;
        this.hasAudioFile = !!this.projectData.data.audioFilePath;
        this.useBuiltinAudio = !!this.projectData.data.useBuiltinAudio;

        // 获取章节标题
        if (this.projectData.data.currentChapter) {
          this.localChapterTitle = this.projectData.data.currentChapter;
          console.log('从项目数据中获取章节标题:', this.localChapterTitle);
        } else if (this.projectData.currentChapter) {
          this.localChapterTitle = this.projectData.currentChapter;
          console.log('从项目对象中获取章节标题:', this.localChapterTitle);
        } else {
          // 尝试从文件路径中提取章节名称
          const srtPath = this.projectData.data.srtFilePath;
          if (srtPath) {
            const pathParts = srtPath.split('/');
            if (pathParts.length >= 3) {
              // 假设路径格式为 draft/项目名/章节名/文件名
              this.localChapterTitle = pathParts[2];
              console.log('从文件路径中提取章节标题:', this.localChapterTitle);
            }
          }
        }

        // 如果项目已经有行数据，加载它们
        if (this.projectData.data.rows && this.projectData.data.rows.length > 0) {
          console.log('使用项目中已有的行数据, 行数:', this.projectData.data.rows.length);

          // 确保所有行数据都有正确的属性，特别是合并和分拆相关的属性
          this.rows = this.projectData.data.rows.map((row, rowIndex) => {
            // 确保每行都有 originalIndex 属性 - 这是新的关键属性
            if (row.originalIndex === undefined) {
              row.originalIndex = row.index || rowIndex + 1;
            }

            // 🔥 统一数据结构：为所有行初始化 mergedRows
            if (!row.mergedRows) {
              // 对于未合并的行，mergedRows 包含行本身的数据
              row.mergedRows = [{
                originalIndex: row.originalIndex,
                index: row.index || rowIndex + 1,
                startTime: row.startTime,
                endTime: row.endTime,
                duration: row.duration,
                description: row.description,
                selectedTags: [...(row.selectedTags || [])],
                keywords: row.keywords || '',
                keywordsMetadata: { ...(row.keywordsMetadata || { autoContent: '', manualContent: '', lastManualEdit: 0 }) },
                imageSrc: row.imageSrc || '',
                imageAlt: row.imageAlt || '',
                isImageLocked: row.isImageLocked || false,
                thumbnails: Array.isArray(row.thumbnails) ? [...row.thumbnails] : [],
                generatedImages: Array.isArray(row.generatedImages) ? [...row.generatedImages] : []
              }];

              // 确保 isMerged 状态正确
              if (row.isMerged === undefined) {
                row.isMerged = false;
              }


            }

            // 🔥 统一数据结构：移除旧的 originalState 字段
            if (row.originalState) {
              delete row.originalState;
            }

            // 🔥 统一数据结构：清理 mergedRows 中的 originalState
            if (row.mergedRows && Array.isArray(row.mergedRows)) {
              row.mergedRows.forEach((subRow) => {
                if (subRow.originalState) {
                  delete subRow.originalState;
                }
              });
            }

            // 确保 isMerged 属性存在
            if (row.isMerged === undefined) {
              row.isMerged = false;
            }

            // 确保 mergedRows 属性存在（如果是合并行）
            if (row.isMerged && !row.mergedRows) {
              row.mergedRows = [];
            }

            // 确保 isSelected 属性存在
            if (row.isSelected === undefined) {
              row.isSelected = false;
            }

            // 确保 selectedTags 属性存在
            if (!row.selectedTags || !Array.isArray(row.selectedTags)) {
              row.selectedTags = [];
            }

            // 🆕 确保 isLocked 属性存在
            if (row.isLocked === undefined) {
              row.isLocked = false;
            }

            // 🔥 关键修复：如果是合并行，确保所有合并项都有正确的索引
            if (row.isMerged && row.mergedRows && row.mergedRows.length > 0) {


              const currentRowIndex = rowIndex + 1; // 当前合并行的index

              row.mergedRows = row.mergedRows.map((mergedRow, mrIndex) => {
                // 🔥 修复：不要重新生成originalIndex，保持原有的值
                if (mergedRow.originalIndex === undefined) {
                  // 如果确实没有originalIndex，使用合理的后备值
                  mergedRow.originalIndex = mergedRow.index || (currentRowIndex + mrIndex);
                }

                // 🔥 关键修复：子项的index应该指向合并后的行索引
                mergedRow.index = currentRowIndex;

                // 🔥 统一数据结构：不再创建或维护 originalState

                return mergedRow;
              });


            }

            return row;
          });

          // 🆕 设置合并标志 - 使用新的数据持久化系统
          const mergedRows = this.rows.filter(row => row.isMerged);
          if (mergedRows.length > 0) {
            this.dataPersistence.setMergeFlag(this.localProjectTitle, this.localChapterTitle, true);
          }

          this.uiStateStudio.setLoadingState(false);
        }
      } else {
        console.warn('项目数据不完整');
        this.uiStateStudio.setLoadingState(false);
      }

      // 加载全局选择的角色状态
      this.loadGlobalSelectedCharacters();
    },
    // 从项目数据中加载SRT内容 - 简化版
    async loadSrtContent() {
      try {
        console.log('开始加载SRT内容');

        // 检查是否有合并操作的标志
        const hasMerged = localStorage.getItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`);
        console.log('合并操作标志:', hasMerged, '项目标题:', this.localProjectTitle, '章节标题:', this.localChapterTitle);

        // 先尝试从project-data.json文件加载数据（如果有合并状态）
        if (hasMerged === 'true') {
          console.log('检测到合并操作标志，尝试从project-data.json文件加载数据');
          const success = await this.loadProjectDataFromFile(this.localProjectTitle, this.localChapterTitle);
          if (success) {
            console.log('成功从project-data.json加载数据（含合并状态）');
            this.uiStateStudio.setLoadingState(false);
            this.loadGlobalSelectedCharacters();
            return;
          } else {
            console.warn('无法从project-data.json加载数据，继续尝试其他方法');
          }
        }

        // 检查项目数据中是否已经有行数据（包含合并状态）
        if (this.projectData.data?.rows?.length > 0) {
          console.log('项目数据中已有行数据，数量:', this.projectData.data.rows.length);
          const existingMergedRows = this.projectData.data.rows.filter(row => row.isMerged);
          console.log('已存在的合并行数量:', existingMergedRows.length);

          if (existingMergedRows.length > 0) {
            // 直接使用现有行数据
            console.log('使用已有的行数据（保留合并状态）');
            this.rows = this.projectData.data.rows;
            this.uiStateStudio.setLoadingState(false);
            this.showSuccessMessage('数据加载成功', `成功加载 ${this.rows.length} 条字幕（保留合并状态）`);
            localStorage.setItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`, 'true');

            // 加载全局选择的角色状态
            this.loadGlobalSelectedCharacters();
            return;
          }
        }

        // 如果项目有SRT内容，直接解析
        if (this.projectData.data?.srtContent) {
          const parsedSubtitles = this.dataManagementStudio.parseSrtContent(this.projectData.data.srtContent);
          console.log('从项目数据解析SRT，字幕数量:', parsedSubtitles.length);
          this.dataManagementStudio.convertSubtitlesToRows(this, parsedSubtitles);
          this.uiStateStudio.setLoadingState(false);
          this.loadGlobalSelectedCharacters();
          return;
        }

        // 如果有SRT文件路径，尝试读取SRT文件内容
        if (this.projectData.data?.srtFilePath) {
          try {
            console.log('项目中有SRT文件路径，尝试读取文件:', this.projectData.data.srtFilePath);

            // 首先检查文件是否存在
            const checkResponse = await fetch(`/api/local/check-file-exists?filePath=${encodeURIComponent(this.projectData.data.srtFilePath)}`);
            const checkResult = await checkResponse.json();

            if (!checkResult.exists) {
              console.error('SRT文件不存在:', this.projectData.data.srtFilePath);
              throw new Error('SRT文件不存在');
            }

            console.log('SRT文件存在，开始读取内容');

            // 读取SRT文件内容
            const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(this.projectData.data.srtFilePath)}`);

            if (!response.ok) {
              console.error('SRT文件读取失败，状态码:', response.status);
              throw new Error(`无法读取SRT文件: ${response.status}`);
            }

            const result = await response.json();

            if (!result.success || !result.content) {
              console.error('SRT文件读取结果无效:', result);
              throw new Error('SRT文件内容无效');
            }

            const srtContent = result.content;

            // 保存SRT内容到项目数据
            this.projectData.data.srtContent = srtContent;

            // 解析SRT内容并转换为行数据
            const parsedSubtitles = this.dataManagementStudio.parseSrtContent(srtContent);
            console.log('SRT解析完成，字幕数量:', parsedSubtitles.length);

            if (parsedSubtitles.length === 0) {
              console.error('解析SRT内容后没有字幕');
              throw new Error('SRT解析结果为空');
            }

            this.dataManagementStudio.convertSubtitlesToRows(this, parsedSubtitles);

            // 使用防抖保存项目数据
            this.debouncedSaveProject();
            this.uiStateStudio.setLoadingState(false);
            this.loadGlobalSelectedCharacters();
            return;
          } catch (error) {
            console.error('读取SRT文件出错:', error);
            this.showErrorMessage(`读取SRT文件失败: ${error.message}`);
          }
        } else {
          console.warn('项目数据中没有SRT文件路径');
        }

        // 如果以上方法都失败，显示无数据状态
        console.warn('无法获取SRT内容，显示无数据状态');
        this.showErrorMessage('无法加载SRT文件内容，请检查SRT文件路径或重新上传SRT文件');
      } catch (error) {
        console.error('加载SRT内容失败:', error);
        this.showErrorMessage('加载SRT内容失败: ' + error.message);
      } finally {
        this.uiStateStudio.setLoadingState(false);

        // 确保在所有情况下都加载全局选择的角色状态
        this.loadGlobalSelectedCharacters();
      }
    },

    // 简化版读取文件内容的方法 - 使用新的数据管理系统
    async readFileContent(filePath) {
      return await this.dataManagementStudio.readFileContent(this, filePath);
    },



    // 🆕 获取服务器基础URL - 使用新的项目上下文管理系统
    async getServerBaseUrl() {
      return await this.projectContextStudio.getServerBaseUrl();
    },

    // 🆕 构建API URL - 使用新的项目上下文管理系统
    getApiUrl(path) {
      return this.projectContextStudio.getApiUrl(path);
    },

    // 🆕 从文件系统加载项目数据 - 使用新的数据管理系统
    async loadProjectDataFromFile(projectTitle, chapterTitle) {
      return await this.dataManagementStudio.loadProjectDataFromFile(this, projectTitle, chapterTitle);
    },

    // 🆕 重复的加载项目数据方法已移除，使用新的数据持久化系统

    // 🆕 显示成功消息 - 使用新的Toast composable
    showSuccessMessage(title, message) {
      // 使用新的Toast composable
      this.toastNotification.showSuccessMessage(title, message);
    },

    // 🆕 显示错误消息 - 使用新的Toast composable
    showErrorMessage(message, title = '错误') {
      // 使用新的Toast composable
      this.toastNotification.showErrorMessage(message, title);
    },

    // 🆕 显示信息消息 - 使用新的Toast composable
    showInfoMessage(title, message) {
      // 使用新的Toast composable
      this.toastNotification.showInfoMessage(title, message);
    },

    // 解析SRT内容 - 使用新的数据管理系统
    parseSrtContent(srtContent) {
      return this.dataManagementStudio.parseSrtContent(srtContent);
    },

    // 时间字符串转换为秒数 - 保持不变
    timeToSeconds(timeStr) {
      try {
        const [h, m, s] = timeStr.split(':').map(Number);
        return h * 3600 + m * 60 + s;
      } catch (error) {
        console.error('时间转换错误:', error, timeStr);
        return 0;
      }
    },

    // 秒数转换为格式化时间 - 保持不变
    formatTime(seconds) {
      const h = Math.floor(seconds / 3600);
      const m = Math.floor((seconds % 3600) / 60);
      const s = Math.floor(seconds % 60);
      return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    },

    // 格式化持续时间 - 保持不变
    formatDuration(seconds) {
      const minutes = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    },

    // 转换字幕数据为行数据 - 使用新的数据管理系统
    convertSubtitlesToRows(subtitles, forceRefresh = false) {
      this.dataManagementStudio.convertSubtitlesToRows(this, subtitles, forceRefresh);
    },

    // 🆕 导航方法 - 使用新的项目上下文管理系统
    goBack() {
      this.projectContextStudio.goBack(this);
    },

    goToNextStep() {
      this.projectContextStudio.goToNextStep();
    },

    // 🆕 更新项目标题 - 使用新的项目上下文管理系统
    updateProjectTitle(newTitle) {
      this.projectContextStudio.updateProjectTitle(this, newTitle);
    },

    // 🆕 导航到指定步骤 - 使用新的项目上下文管理系统
    navigateToStep(stepName) {
      this.projectContextStudio.navigateToStep(this, stepName);
    },

    // 🆕 处理导出导入 - 使用新的项目上下文管理系统
    handleExportImport() {
      this.projectContextStudio.handleExportImport(this);
    },

    // 🆕 处理标签页变化 - 使用新的项目上下文管理系统
    handleTabChange(tabName) {
      this.projectContextStudio.handleTabChange(this, tabName);
    },

    // 🆕 处理导航操作 - 使用新的项目上下文管理系统
    handleNavAction(action) {
      console.log('🎯 [ContentCreationStudio] 接收到导航操作:', action);
      console.log('🎯 [ContentCreationStudio] 调用 projectContextStudio.handleNavAction');
      this.projectContextStudio.handleNavAction(this, action);
    },

    // 强制刷新SRT内容的方法
    async forceRefreshSrtContent() {
      try {
        if (!this.projectData?.data?.srtFilePath) {
          this.showErrorMessage('无法刷新：未找到SRT文件路径');
          return false;
        }

        // 显示确认对话框
        const confirmMessage = `警告：强制刷新将使用最新的SRT文件内容替换当前所有字幕行，并丢失所有合并操作。确定要继续吗？`; // 更新确认消息
        if (!confirm(confirmMessage)) {
          console.log('用户取消了强制刷新');
          return false;
        }

        console.log('执行强制刷新，使用文件路径:', this.projectData.data.srtFilePath);
        this.uiStateStudio.setLoadingState(true, '正在强制刷新SRT数据...'); // 开始加载状态

        // 1. 读取SRT文件内容
        const srtContentResponse = await fetch(`/api/local/read-file?path=${encodeURIComponent(this.projectData.data.srtFilePath)}`);

        if (!srtContentResponse.ok) {
          console.error('无法读取SRT文件内容:', srtContentResponse.status);
          throw new Error(`无法读取SRT文件: ${srtContentResponse.status}`); // 抛出错误以便捕获
        }

        const srtResult = await srtContentResponse.json();

        if (!srtResult.success || !srtResult.content) {
          console.error('SRT文件内容无效');
          throw new Error('SRT文件内容无效'); // 抛出错误
        }

        // 2. 解析SRT内容
        const subtitles = this.dataManagementStudio.parseSrtContent(srtResult.content);

        if (!subtitles || subtitles.length === 0) {
          console.error('无法解析SRT内容或内容为空');
          throw new Error('无法解析SRT内容或内容为空'); // 抛出错误
        }
        console.log(`成功解析SRT，共 ${subtitles.length} 条字幕`);

        // 3. 创建全新的行数据，替换旧数据
        console.log('[Performance] 强制刷新：开始创建行数据，使用浅拷贝优化');
        const createStartTime = Date.now();

        this.rows = subtitles.map((subtitle, index) => {
          // 使用浅拷贝替代深拷贝，提升性能
          const row = {
            // 直接复制emptyRow的属性，避免JSON序列化
            isSelected: false,
            description: subtitle.text,
            keywords: '',
            imageSrc: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
            imageAlt: '',
            isImageLocked: false,
            isLocked: false, // 🆕 添加行锁定状态
            thumbnails: [],
            // 新增属性
            startTime: subtitle.startTime,
            endTime: subtitle.endTime,
            duration: subtitle.duration,
            index: index + 1, // 显示序号，从1开始
            originalIndex: subtitle.index || (index + 1), // 原始序号
            // 🔥 统一数据结构：使用 mergedRows 替代 originalState
            mergedRows: [{
              originalIndex: subtitle.index || (index + 1),
              index: index + 1,
              startTime: subtitle.startTime,
              endTime: subtitle.endTime,
              duration: subtitle.duration,
              description: subtitle.text,
              selectedTags: [],
              keywords: '',
              keywordsMetadata: { autoContent: '', manualContent: '', lastManualEdit: 0 },
              imageSrc: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
              imageAlt: '',
              isImageLocked: false,
              thumbnails: [],
              generatedImages: []
            }],
            isMerged: false, // 强制重置为非合并状态
            tags: [], // 空标签数组
            selectedTags: [] // 用户选择的标签数组
          };

          return row;
        });

        const createEndTime = Date.now();
        console.log(`[Performance] 强制刷新：行数据创建完成，耗时: ${createEndTime - createStartTime}ms`);
        console.log('已创建全新的行数据，行数:', this.rows.length);

        // 4. 更新项目数据并保存
        if (this.projectData.data) {
          this.projectData.data.rows = this.rows;
          this.projectData.data.srtContent = srtResult.content; // 保存新的SRT内容
          // 清除本地存储中的合并标志
          localStorage.removeItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`);
          console.log('[Performance] 已清除合并状态标志');

          // 使用防抖保存而非立即保存
          this.debouncedSaveProject();
        }

        this.uiStateStudio.setLoadingState(false); // 结束加载状态
        this.showSuccessMessage('强制刷新成功', `已加载 ${this.rows.length} 条新字幕`);
        return true; // 返回成功

      } catch (error) {
        console.error('强制刷新SRT内容时出错:', error);
        this.showErrorMessage('强制刷新失败', error.message);
        this.uiStateStudio.setLoadingState(false); // 确保错误时也结束加载
        return false; // 返回失败
      }
    },

    // 🆕 次级导航栏相关方法已迁移到项目上下文管理系统

    // 🆕 导航操作方法已迁移到项目上下文管理系统

    // 🆕 同步按钮状态到SecondaryNavbar - 使用新的批量操作管理系统
    syncButtonStates() {
      this.batchOperationsStudio.syncButtonStates(this);
    },

    // 🆕 显示Toast消息 - 使用新的Toast composable，兼容新旧调用方式
    showToast(typeOrOptions, title, message) {
      // 使用新的Toast composable
      this.toastNotification.showToast(typeOrOptions, title, message);
    },

    // 🆕 显示警告消息 - 使用新的Toast composable
    showWarningMessage(title, message) {
      // 使用新的Toast composable
      this.toastNotification.showWarningMessage(title, message);
    },

    // 🔧 重置批量操作状态
    resetBatchOperationStates() {
      console.log('🔄 [批量操作] 重置所有批量操作状态');

      if (this.batchOperationUI) {
        this.batchOperationUI.resetAllStates();
        console.log('✅ [批量操作] UI状态已重置');
      }

      if (this.batchStateManager) {
        this.batchStateManager.clearAllCallbacks();
        console.log('✅ [批量操作] 状态管理器已清理');
      }

      // 同步按钮状态
      this.$nextTick(() => {
        this.syncButtonStates();
      });
    },

    // 🆕 调试批量操作状态 - 使用新的调试工具
    debugBatchOperationStates() {
      // 使用新的调试工具
      return this.debugUtils.debugBatchStates(this);
    },

    // 🆕 批量推理功能 - 使用新的批量操作管理系统
    async performBatchInference() {
      await this.batchOperationsStudio.performBatchInference(this);
    },

    // 🆕 批量推理取消功能 - 使用新的批量操作管理系统
    async performBatchInferenceCancel() {
      await this.batchOperationsStudio.performBatchInferenceCancel(this);
    },

    // 🆕 批量图像生成功能 - 使用新的批量操作管理系统
    async performBatchImageGeneration() {
      await this.batchOperationsStudio.performBatchImageGeneration(this);
    },

    // 打开图像提示词生成器
    openImagePromptGenerator() {
      console.log('打开图像提示词生成器，选中行数:', this.selectedRows.length);

      // 检查是否有选中的行
      if (this.selectedRows.length === 0) {
        this.showErrorMessage('请先选择需要生成图像提示词的行');
        // 移除重复的toast调用，showErrorMessage已经调用了toast.show()
        return;
      }

      // 准备上下文文本
      this.globalReasoningText = this.getFormattedTextForReasoning();

      // 打开抽屉
      this.showImagePromptDrawer = true;
    },

    // 🆕 处理图像提示词应用到行数据 - 使用新的提示词推理管理系统
    handleApplyPromptsToRows(results) {
      this.promptReasoningStudio.handleApplyPromptsToRows(this, results);
    },

    // 🆕 行操作方法 - 使用新的行操作系统
    toggleRowSelection(index) {
      this.rowOperations.toggleRowSelection(this, index);
    },

    // 🆕 向上合并行 - 使用新的行操作系统
    handleMergeUp(index) {
      this.rowOperations.mergeUpRow(this, index);
    },

    // 🆕 向下分拆行 - 使用新的行操作系统
    handleSplitDown(index) {
      this.rowOperations.splitDownRow(this, index);
    },

    // 初始化关键词元数据
    initializeKeywordsMetadata(row) {
      if (!row.keywordsMetadata) {
        row.keywordsMetadata = {
          manualContent: '',      // 用户手动输入的内容
          autoContent: '',        // Tag自动生成的内容
          lastManualEdit: 0       // 最后手动编辑时间戳
        };
      }
      return row.keywordsMetadata;
    },

    // 🔥 统一数据结构：不再需要 ensureCompleteOriginalState 函数

    // 智能关键词更新处理
    handleKeywordsUpdated(event) {
      const { rowIndex, keywords: newContent, isAutoGenerated = false } = event;

      if (rowIndex < 0 || rowIndex >= this.rows.length) {
        return;
      }

      const currentRow = this.rows[rowIndex];



      const metadata = this.initializeKeywordsMetadata(currentRow);

      if (isAutoGenerated) {
        // 来自Tag选择的自动生成内容
        metadata.autoContent = newContent || '';
        console.log(`Tag关键词更新[${rowIndex}]: "${newContent}"`);
      } else {
        // 来自用户手动输入
        metadata.manualContent = newContent || '';
        metadata.lastManualEdit = Date.now();
        console.log(`手动关键词更新[${rowIndex}]: "${newContent}"`);
      }

      // 合并显示内容：Tag描述在前，用户输入在后
      const mergedContent = this.mergeKeywords(metadata.autoContent, metadata.manualContent);

      if (currentRow.keywords !== mergedContent) {
        currentRow.keywords = mergedContent;

        // 🔥 关键修复：如果行有 mergedRows，同步更新 mergedRows 中的数据
        if (currentRow.mergedRows && currentRow.mergedRows.length > 0) {
          // 找到对应的 mergedRows 项（通过 originalIndex 匹配）
          const targetItem = currentRow.mergedRows.find(item => item.originalIndex === currentRow.originalIndex);
          if (targetItem) {
            targetItem.keywords = mergedContent;
            targetItem.keywordsMetadata = { ...metadata };
            console.log('[KeywordSync] 同步更新 mergedRows 中的 Keywords:', {
              originalIndex: targetItem.originalIndex,
              keywords: targetItem.keywords,
              isAutoGenerated: isAutoGenerated
            });
          }
        }

        // 保存到项目数据
        if (this.projectData && this.projectData.data) {
          this.projectData.data.rows = this.rows;
          this.debouncedSaveProject();
        }
      }
    },

    // 简单的关键词合并：Tag描述 + 用户输入
    mergeKeywords(autoContent, manualContent) {
      const auto = (autoContent || '').trim();
      const manual = (manualContent || '').trim();

      if (!auto && !manual) return '';
      if (!auto) return manual;
      if (!manual) return auto;

      // Tag描述在前，用户输入在后，用逗号分隔
      return `${auto}, ${manual}`;
    },

    // 获取当前行的Tag描述内容
    getCurrentTagDescriptions(rowIndex) {
      if (rowIndex < 0 || rowIndex >= this.rows.length) {
        return '';
      }

      const currentRow = this.rows[rowIndex];
      const selectedTags = currentRow.selectedTags || [];
      const descriptions = [];

      selectedTags.forEach(tagName => {
        const character = this.currentSelectedCharacters.find(char =>
          (typeof char === 'string' ? char : char.name) === tagName
        );

        if (character && character.description) {
          descriptions.push(character.description.trim());
        }
      });

      return descriptions.join(', ');
    },

    // 从完整关键词中提取用户手动输入的部分
    extractManualContent(fullContent, autoContent) {
      if (!fullContent) return '';
      if (!autoContent) return fullContent;

      // 移除自动内容，保留用户输入
      let manual = fullContent.trim();
      const auto = autoContent.trim();

      // 情况1：内容以自动内容开头 "auto, manual"
      if (manual.startsWith(auto)) {
        manual = manual.substring(auto.length);
        // 移除开头的逗号和空格
        manual = manual.replace(/^,\s*/, '').trim();
      }
      // 情况2：内容以自动内容结尾 "manual, auto" (不太可能，但处理一下)
      else if (manual.endsWith(auto)) {
        manual = manual.substring(0, manual.length - auto.length);
        // 移除结尾的逗号和空格
        manual = manual.replace(/,\s*$/, '').trim();
      }
      // 情况3：自动内容在中间 "manual1, auto, manual2" (复杂情况)
      else if (manual.includes(auto)) {
        // 简单处理：移除自动内容及其前后的逗号
        manual = manual.replace(auto, '');
        manual = manual.replace(/,\s*,/g, ','); // 移除连续逗号
        manual = manual.replace(/^,\s*/, '').replace(/,\s*$/, ''); // 移除开头结尾的逗号
        manual = manual.trim();
      }

      return manual;
    },

    // 兼容旧的updateKeywords方法（用于KeywordCell的直接输入）
    updateKeywords(index, value) {
      if (index < 0 || index >= this.rows.length) {
        return;
      }

      // 获取当前的Tag描述
      const currentTagDescriptions = this.getCurrentTagDescriptions(index);

      // 从用户输入中提取纯手动内容
      const manualContent = this.extractManualContent(value, currentTagDescriptions);

      console.log(`KeywordCell输入解析[${index}]: 完整="${value}" -> Tag="${currentTagDescriptions}" | 手动="${manualContent}"`);

      this.handleKeywordsUpdated({
        rowIndex: index,
        keywords: manualContent,
        isAutoGenerated: false
      });
    },

    // 🔥 新增：处理清除Tag标签的方法
    handleClearTags(event) {
      const { rowIndex } = event;

      if (rowIndex < 0 || rowIndex >= this.rows.length) {
        console.warn('[ClearTags] 无效的行索引:', rowIndex);
        return;
      }

      const currentRow = this.rows[rowIndex];

      console.log(`[ClearTags] 清除第${rowIndex + 1}行的Tag标签`);

      // 清除当前行的selectedTags
      if (currentRow.selectedTags && currentRow.selectedTags.length > 0) {
        const clearedTags = [...currentRow.selectedTags];
        currentRow.selectedTags = [];

        console.log(`[ClearTags] 已清除标签:`, clearedTags);

        // 🔥 统一数据结构：同时清除mergedRows中的标签
        if (currentRow.isMerged && currentRow.mergedRows && Array.isArray(currentRow.mergedRows)) {
          currentRow.mergedRows.forEach((subRow, subIndex) => {
            if (subRow.selectedTags && subRow.selectedTags.length > 0) {
              subRow.selectedTags = [];
              console.log(`[ClearTags] 已清除mergedRows[${subIndex}]的标签`);
            }
          });
        }

        // 🔥 修复：确保项目数据同步更新
        if (this.projectData && this.projectData.data) {
          // 同步更新项目数据中的行数据
          this.projectData.data.rows = this.rows;
          console.log(`[ClearTags] 已同步更新项目数据中的行数据`);
        }

        // 🔥 修复：使用立即保存而不是防抖保存，确保用户操作立即生效
        this.saveProjectDataImmediately();

        // 显示成功消息
        this.showSuccessMessage('标签已清除', `已清除第${rowIndex + 1}行的所有标签`);
      } else {
        console.log(`[ClearTags] 第${rowIndex + 1}行没有需要清除的标签`);
      }
    },

    // 处理角色选择应用
    handleApplyCharacters(rowIndex, selectedCharacters) {
      console.log('ContentCreationStudio - 接收到角色应用请求:', { rowIndex, selectedCharacters });
      if (rowIndex >= 0 && rowIndex < this.rows.length) {
        // 更新行的标签数据
        this.rows[rowIndex].tags = selectedCharacters.map(name => ({ name, type: 'character' }));
        console.log('ContentCreationStudio - 已更新行标签:', this.rows[rowIndex].tags);

        // 保存项目数据
        if (this.projectData.data) {
          this.projectData.data.rows = this.rows;
          this.debouncedSaveProject();
        }

        // 显示成功消息
        this.showSuccessMessage('角色标签已更新', `已为第${rowIndex + 1}行应用${selectedCharacters.length}个角色标签`);
      }
    },

    // 清理重复的角色标签数据
    cleanupDuplicateTagsData() {
      console.log('ContentCreationStudio - 开始清理重复的角色标签数据');

      if (!this.rows || this.rows.length === 0) {
        console.log('ContentCreationStudio - 没有行数据需要清理');
        return;
      }

      let cleanedCount = 0;
      this.rows.forEach((row, index) => {
        if (row.tags && row.tags.length > 0) {
          // 清空所有行的标签，让用户重新选择
          row.tags = [];
          cleanedCount++;
          console.log(`ContentCreationStudio - 已清理第${index + 1}行的标签数据`);
        }
      });

      if (cleanedCount > 0) {
        // 保存清理后的数据
        if (this.projectData.data) {
          this.projectData.data.rows = this.rows;
          this.debouncedSaveProject();
        }

        this.showSuccessMessage(
          '数据清理完成',
          `已清理${cleanedCount}行的重复标签数据，请重新选择每行需要的角色标签`
        );
      }
    },

    // 应用角色到所有行 - 只更新全局可选角色，不直接添加到每行
    applyCharactersToAllSelected(selectedCharacters) {
      console.log('ContentCreationStudio - 接收到批量应用角色请求:', selectedCharacters);
      console.log('ContentCreationStudio - 当前行数据总数:', this.rows.length);

      if (!this.rows || this.rows.length === 0) {
        console.warn('ContentCreationStudio - 没有行数据');
        this.showErrorMessage('没有可应用角色的行数据');
        return;
      }

      // 更新当前选中的角色状态（这些角色将在TagsCell中显示为可选项）
      this.currentSelectedCharacters = selectedCharacters;
      console.log('ContentCreationStudio - 更新当前选中角色状态:', this.currentSelectedCharacters);

      // 保存全局选择状态到项目数据
      this.saveGlobalSelectedCharacters();

      // 显示成功消息 - 说明角色现在可以在每行中选择
      this.showSuccessMessage(
        '可选角色已更新',
        `已设置${selectedCharacters.length}个可选角色，请在每行的标签列中点击选择需要的角色`
      );

      // 通知父组件关闭全局推理抽屉
      this.$emit('close-global-reasoning-drawer');
    },

    // 处理标签选择变化
    handleTagSelectionChanged(event) {
      console.log('ContentCreationStudio - 标签选择变化:', event);

      const { rowIndex, selectedTags } = event;

      // 确保行索引有效
      if (rowIndex >= 0 && rowIndex < this.rows.length) {
        const currentRow = this.rows[rowIndex];

        // 更新行数据中的选择状态
        if (!currentRow.selectedTags) {
          currentRow.selectedTags = [];
        }
        currentRow.selectedTags = [...selectedTags];

            // 🔥 关键修复：如果行有 mergedRows，同步更新 mergedRows 中的数据
        if (currentRow.mergedRows && currentRow.mergedRows.length > 0) {
          // 找到对应的 mergedRows 项（通过 originalIndex 匹配）
          const targetItem = currentRow.mergedRows.find(item => item.originalIndex === currentRow.originalIndex);
          if (targetItem) {
            targetItem.selectedTags = [...selectedTags];
            console.log('[TagSync] 同步更新 mergedRows 中的 Tags:', {
              originalIndex: targetItem.originalIndex,
              selectedTags: targetItem.selectedTags
            });
          }
        }

        // 🔥 关键修复：如果是合并行，需要同时更新mergedRows中对应的子项
        if (currentRow.isMerged && currentRow.mergedRows && currentRow.mergedRows.length > 0) {
          console.log('[TagSelection] 处理合并行标签选择，当前标签:', selectedTags);

          // 🔥 统一数据结构：简化标签变化检测，不再依赖 originalState
          const previousTags = currentRow.selectedTags || [];
          const newTags = selectedTags.filter(tag => !previousTags.includes(tag));
          const removedTags = previousTags.filter(tag => !selectedTags.includes(tag));

          console.log('[TagSelection] 标签变化分析:', {
            previousTags,
            selectedTags,
            newTags,
            removedTags
          });

          // 🔥 核心逻辑：将新增的标签分配给第一个没有该标签的子项
          if (newTags.length > 0 || removedTags.length > 0) {
            console.log('[TagSelection] 开始处理标签变化，mergedRows数量:', currentRow.mergedRows.length);

            newTags.forEach(tagName => {
              // 🔥 简化逻辑：找到第一个没有该标签的子项，将标签分配给它
              const targetSubRow = currentRow.mergedRows.find(subRow =>
                !subRow.selectedTags || !subRow.selectedTags.includes(tagName)
              );

              if (targetSubRow) {
                if (!targetSubRow.selectedTags) {
                  targetSubRow.selectedTags = [];
                }
                targetSubRow.selectedTags.push(tagName);
                console.log(`[TagSelection] 添加标签"${tagName}"到mergedRows[${targetSubRow.originalIndex}]:`, targetSubRow.selectedTags);

                // 🔥 统一数据结构：不再需要更新 originalState
              } else {
                console.log(`[TagSelection] 警告：未找到可分配标签"${tagName}"的子项`);
              }
            });

            removedTags.forEach(tagName => {
              // 🔥 移除逻辑：从所有包含该标签的子项中移除
              currentRow.mergedRows.forEach(subRow => {
                if (subRow.selectedTags && subRow.selectedTags.includes(tagName)) {
                  subRow.selectedTags = subRow.selectedTags.filter(tag => tag !== tagName);
                  console.log(`[TagSelection] 移除标签"${tagName}"从mergedRows[${subRow.originalIndex}]:`, subRow.selectedTags);

                  // 🔥 统一数据结构：不再需要更新 originalState
                }
              });
            });
          }

          // 🔥 统一数据结构：不再需要更新 originalState

          // 输出调试信息
          console.log('[TagSelection] mergedRows更新后的状态:', currentRow.mergedRows.map(subRow => ({
            originalIndex: subRow.originalIndex,
            selectedTags: subRow.selectedTags,
            originalStateSelectedTags: subRow.originalState?.selectedTags
          })));
        }

        // 🔥 统一数据结构：不再需要为非合并行创建或更新 originalState

        console.log(`ContentCreationStudio - 已保存第${rowIndex + 1}行的标签选择状态:`, selectedTags);

        // 使用防抖保存到项目数据
        this.saveTagSelectionToProject();
      }
    },

    // 保存标签选择状态到项目数据 - 优化版
    saveTagSelectionToProject() {
      if (this.projectData && this.projectData.data) {
        // 更新项目数据中的行数据
        this.projectData.data.rows = this.rows;

        // 使用防抖保存而非立即保存
        this.debouncedSaveProject();

        console.log('[Performance] 标签选择状态已标记为待保存');
      }
    },

    // 保存全局选择的角色状态到项目数据 - 使用新的数据管理系统
    saveGlobalSelectedCharacters() {
      this.dataManagementStudio.saveGlobalSelectedCharacters(this);
    },

    // 从项目数据加载全局选择的角色状态 - 使用新的数据管理系统
    loadGlobalSelectedCharacters() {
      this.dataManagementStudio.loadGlobalSelectedCharacters(this);
    },

    // 加载角色数据
    async loadCharacterData() {
      try {
        if (!this.localProjectTitle || !this.localChapterTitle) {
          console.warn('缺少项目或章节信息，无法加载角色数据');
          return;
        }

        // 1. 加载Current.json中的分析结果
        const currentJsonPath = `draft/${this.localProjectTitle}/${this.localChapterTitle}/Current.json`;
        console.log('ContentCreationStudio - 尝试加载角色数据，路径:', currentJsonPath);

        const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(currentJsonPath)}`);

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.content) {
            const currentData = JSON.parse(result.content);
            console.log('ContentCreationStudio - Current.json数据:', currentData);

            // 设置当前分析结果
            this.currentAnalysisResults = currentData;

            // 提取角色数据
            if (currentData.characters && Array.isArray(currentData.characters)) {
              this.availableCharacters = currentData.characters.filter(char => char.type === 'character');
              console.log('ContentCreationStudio - 成功加载角色数据:', this.availableCharacters.length, '个角色', this.availableCharacters);
            } else {
              console.warn('ContentCreationStudio - Current.json中没有角色数据');
            }
          }
        } else {
          console.warn('ContentCreationStudio - 无法读取Current.json文件，可能文件不存在');
        }

        // 2. 加载Global.json中的预设角色
        const globalJsonPath = `draft/${this.localProjectTitle}/Global.json`;
        console.log('ContentCreationStudio - 尝试加载预设角色数据，路径:', globalJsonPath);

        const globalResponse = await fetch(`/api/local/read-file?path=${encodeURIComponent(globalJsonPath)}`);

        if (globalResponse.ok) {
          const globalResult = await globalResponse.json();
          if (globalResult.success && globalResult.content) {
            const globalData = JSON.parse(globalResult.content);
            console.log('ContentCreationStudio - Global.json数据:', globalData);

            // 提取预设角色数据
            if (globalData.presetCharacters && Array.isArray(globalData.presetCharacters)) {
              this.presetCharacters = globalData.presetCharacters;
              console.log('ContentCreationStudio - 成功加载预设角色数据:', this.presetCharacters.length, '个预设角色');
            } else {
              console.warn('ContentCreationStudio - Global.json中没有预设角色数据');
              this.presetCharacters = [];
            }
          }
        } else {
          console.warn('ContentCreationStudio - 无法读取Global.json文件，可能文件不存在');
          this.presetCharacters = [];
        }

      } catch (error) {
        console.error('ContentCreationStudio - 加载角色数据时出错:', error);
        // 确保在出错时也有默认值
        this.presetCharacters = [];
        this.currentAnalysisResults = null;
      }
    },



    // 🆕 图片操作 - 使用新的图像生成管理系统
    handleSelectMainImage(index) {
      this.imageGenerationStudio.handleSelectMainImage(this, index);
    },

    handleToggleImageLock(index) {
      if (index >= 0 && index < this.rows.length) {
        this.rows[index].isImageLocked = this.toggleImageLock(this.rows[index].isImageLocked);
        this.showInfoMessage('锁定状态', `第${index+1}行图片锁定状态已${this.rows[index].isImageLocked ? '开启' : '关闭'}`);
      }
    },

    handleClearMainImage(index) {
      if (index >= 0 && index < this.rows.length) {
        this.rows[index].imageSrc = this.clearMainImage();
        this.rows[index].imageAlt = '';
        this.showInfoMessage('清除成功', `第${index+1}行主图已清除`);
      }
    },

    // 🆕 删除主图 - 使用新的图像生成管理系统
    async handleDeleteMainImage(index) {
      await this.imageGenerationStudio.handleDeleteMainImage(this, index);
    },

    // 🆕 删除缩略图 - 使用新的图像生成管理系统
    async handleDeleteThumbnail(rowIndex, thumbnailIndex) {
      await this.imageGenerationStudio.handleDeleteThumbnail(this, rowIndex, thumbnailIndex);
    },

    // 🆕 缩略图操作 - 使用新的图像生成管理系统
    handleSelectThumbnail(rowIndex, thumbnailIndex) {
      this.imageGenerationStudio.handleSelectThumbnail(this, rowIndex, thumbnailIndex);
    },

    // 🆕 管理图片 - 使用新的图像生成管理系统
    handleManageImages(index) {
      this.imageGenerationStudio.handleManageImages(this, index);
    },

    // 🆕 关闭图片管理窗口 - 使用新的图像生成管理系统
    closeImageManagementModal() {
      this.imageGenerationStudio.closeImageManagementModal(this);
    },

    // 🆕 从模态窗口删除主图 - 使用新的图像生成管理系统
    async handleDeleteMainImageFromModal() {
      await this.imageGenerationStudio.handleDeleteMainImageFromModal(this);
    },

    // 🆕 从模态窗口删除可选图 - 使用新的图像生成管理系统
    async handleDeleteOptionalImageFromModal(thumbnailIndex) {
      await this.imageGenerationStudio.handleDeleteOptionalImageFromModal(this, thumbnailIndex);
    },

    // 🆕 从模态窗口上传图片 - 使用新的图像生成管理系统
    async handleUploadImagesFromModal(files) {
      await this.imageGenerationStudio.handleUploadImagesFromModal(this, files);
    },

    // 🆕 从模态窗口处理上传错误 - 使用新的图像生成管理系统
    handleUploadErrorFromModal(errorMessage) {
      this.imageGenerationStudio.handleUploadErrorFromModal(this, errorMessage);
    },

    // 🆕 从模态窗口替换主图 - 使用新的图像生成管理系统
    async handleReplaceMainImageFromModal(file) {
      await this.imageGenerationStudio.handleReplaceMainImageFromModal(this, file);
    },

    // 🆕 从模态窗口替换可选图 - 使用新的图像生成管理系统
    async handleReplaceOptionalImageFromModal({ file, index }) {
      await this.imageGenerationStudio.handleReplaceOptionalImageFromModal(this, { file, index });
    },

    // 🆕 图片保存和转换方法已迁移到 useImageManagement composable

    // 🆕 操作列按钮 - 使用新的图像生成管理系统
    handleRedrawImage(index) {
      this.imageGenerationStudio.handleRedrawImage(this, index);
    },

    // 🆕 处理单行提示词推理 - 使用新的提示词推理管理系统
    async handleInferPrompt(index, isBatchMode = false) {
      return await this.promptReasoningStudio.handleInferPrompt(this, index, isBatchMode);
    },

    // 🆕 预览提示词功能 - 使用新的提示词推理管理系统
    async handlePreviewPrompt(index) {
      await this.promptReasoningStudio.handlePreviewPrompt(this, index);
    },

    // 🆕 处理确认发送提示词 - 使用新的提示词推理管理系统
    async handleConfirmSendPrompt(promptData) {
      await this.promptReasoningStudio.handleConfirmSendPrompt(this, promptData);
    },

    // 🆕 处理关闭测试窗口 - 使用新的提示词推理管理系统
    handleCloseTestModal() {
      this.promptReasoningStudio.handleCloseTestModal(this);
    },

    // 🆕 处理角色标签自动应用 - 使用新的提示词推理管理系统
    async handleApplyCharacterTags(event) {
      await this.promptReasoningStudio.handleApplyCharacterTags(this, event);
    },



    // 🆕 处理打开图像提示词生成器设置 - 使用新的提示词推理管理系统
    handleOpenImagePromptSettings() {
      this.promptReasoningStudio.handleOpenImagePromptSettings(this);
    },

    // 🆕 处理清除所有行的关键词 - 使用新的提示词推理管理系统
    handleClearAllKeywords() {
      this.promptReasoningStudio.handleClearAllKeywords(this);
    },

    // 添加直接从SRT文件加载内容的方法 - 使用新的数据管理系统
    async forceReadSrtFile() {
      return await this.dataManagementStudio.forceReadSrtFile(this);
    },

    // 强制初始化数据 - 使用新的数据管理系统
    async forceInitializeData() {
      return await this.dataManagementStudio.forceInitializeData(this);
    },

    // 注意：原有的saveProjectDataToFile方法已移除
    // 统一使用debouncedSaveProject()方法，内部调用useDataPersistence.js

    // 添加自动检测和处理SRT文件变更的方法
    async autoDetectSrtChanges() {
      if (!this.localProjectTitle || !this.localChapterTitle) {
        console.warn('无法检测SRT变更：缺少项目或章节信息');
        return false;
      }

      try {
        // 检查是否有更新标志
        const updateKey = `srt_updated_${this.localProjectTitle}_${this.localChapterTitle}`;
        const updateTimestamp = localStorage.getItem(updateKey);

        if (updateTimestamp) {
          console.log('检测到SRT更新标志，时间戳:', updateTimestamp);
          // 移除标志，避免重复处理
          localStorage.removeItem(updateKey);

          // 获取当前目录中的文件列表
          const filesPath = `draft/${this.localProjectTitle}/${this.localChapterTitle}`;
          const filesResponse = await fetch(`/api/local/list-files?path=${encodeURIComponent(filesPath)}`);

          if (!filesResponse.ok) {
            console.error('无法获取目录文件列表');
            return false;
          }

          const files = await filesResponse.json();
          // 查找SRT文件
          const srtFiles = files.filter(file => file.toLowerCase().endsWith('.srt'));

          if (srtFiles.length === 0) {
            console.warn('目录中未找到SRT文件，但有更新标志');
            return false;
          }

          // 如果有多个SRT文件，选择最新的一个
          srtFiles.sort();
          const latestSrtFile = srtFiles[srtFiles.length - 1];

          // 构建SRT文件完整路径
          const srtPath = `${filesPath}/${latestSrtFile}`;
          console.log('发现更新的SRT文件:', srtPath);

          // 刷新SRT内容（总是加载新内容）
          const result = await this.refreshSrtContent(srtPath, true);
          return result;
        }

        // 如果没有更新标志，检查文件变更
        const filesPath = `draft/${this.localProjectTitle}/${this.localChapterTitle}`;
        console.log('检查目录中的SRT文件:', filesPath);
        const filesResponse = await fetch(`/api/local/list-files?path=${encodeURIComponent(filesPath)}`);

        if (!filesResponse.ok) {
          console.error('无法获取目录文件列表');
          return false;
        }

        const files = await filesResponse.json();
        // 查找SRT文件
        const srtFiles = files.filter(file => file.toLowerCase().endsWith('.srt'));

        if (srtFiles.length === 0) {
          console.warn('目录中未找到SRT文件');
          return false;
        }

        // 如果有多个SRT文件，选择最新的一个
        srtFiles.sort();
        const latestSrtFile = srtFiles[srtFiles.length - 1];

        // 构建SRT文件完整路径
        const srtPath = `${filesPath}/${latestSrtFile}`;
        console.log('发现SRT文件:', srtPath);

        // 检查是否与当前加载的文件路径相同
        if (this.projectData?.data?.srtFilePath === srtPath) {
          return false; // 静默跳过，无需刷新
        }

        console.log('发现新的SRT文件路径，准备刷新:', srtPath);

        // 刷新SRT内容
        const result = await this.refreshSrtContent(srtPath, true);
        return result;
      } catch (error) {
        console.error('自动检测SRT变更出错:', error);
        return false;
      }
    },

    // 添加refreshSrtContent方法，用于主动刷新SRT内容
    async refreshSrtContent(newSrtPath, forceRefresh = false) {
      if (!newSrtPath) {
        console.warn('没有提供SRT文件路径，无法刷新内容');
        return false;
      }

      try {
        console.log('开始刷新SRT内容，路径:', newSrtPath);

        // 读取SRT文件内容
        const srtContentResponse = await fetch(`/api/local/read-file?path=${encodeURIComponent(newSrtPath)}`);

        if (!srtContentResponse.ok) {
          console.error('无法读取SRT文件内容:', srtContentResponse.status);
          this.showErrorMessage('无法读取SRT文件内容');
          return false;
        }

        const srtResult = await srtContentResponse.json();

        if (!srtResult.success || !srtResult.content) {
          console.error('SRT文件内容无效');
          this.showErrorMessage('SRT文件内容无效');
          return false;
        }

        console.log('成功读取SRT文件内容，长度:', srtResult.content.length);

        // 解析SRT内容
        const parsedSubtitles = this.parseSrtContent(srtResult.content);
        console.log(`成功解析SRT内容，共${parsedSubtitles.length}条字幕`);

        // 如果解析结果为空，提示错误
        if (parsedSubtitles.length === 0) {
          console.error('SRT解析结果为空，可能格式不正确');
          this.showErrorMessage('SRT解析结果为空，可能格式不正确');
          return false;
        }

        // 更新项目数据
        if (!this.projectData.data) {
          this.projectData.data = {};
        }

        // 保存SRT内容和路径
        this.projectData.data.srtContent = srtResult.content;
        this.projectData.data.srtFilePath = newSrtPath;

        // 提取文件名
        const pathParts = newSrtPath.split('/');
        const srtFileName = pathParts[pathParts.length - 1];
        this.projectData.data.srtFile = srtFileName;

        // 转换为行数据，传递forceRefresh参数
        this.convertSubtitlesToRows(parsedSubtitles, forceRefresh);

        // 更新文件状态
        this.hasSrtFile = true;

        // 如果之前有合并操作，清除合并标记
        if (this.rows.some(row => row.isMerged)) {
          localStorage.removeItem(`project_${this.localProjectTitle}_${this.localChapterTitle}_hasMerged`);
        }

        // 使用防抖保存项目数据
        this.debouncedSaveProject();

        // 显示成功消息
        this.showSuccessMessage('SRT内容已更新', `成功加载 ${parsedSubtitles.length} 条字幕`);

        return true;
      } catch (error) {
        console.error('刷新SRT内容出错:', error);
        this.showErrorMessage('刷新SRT内容失败: ' + error.message);
        return false;
      }
    },

    // 提供一个公共方法，可以被父组件调用来触发刷新
    checkForSrtUpdates() {
      // 检查项目数据是否发生变化
      if (!this.projectData || !this.projectData.data || !this.projectData.data.srtFilePath) {
        return false;
      }

      // 直接使用当前项目中记录的SRT文件路径刷新内容
      return this.refreshSrtContent(this.projectData.data.srtFilePath);
    },

    // 添加行
    addRow() {
      this.rows.push(JSON.parse(JSON.stringify(this.emptyRow)));
    },

    // 🆕 处理导出/导入事件已迁移到项目上下文管理系统



    // 接收并处理从AdjustShotsDrawer返回的行数据
    updateRowsFromDrawer(updatedRows) {
      console.log('ContentCreationStudio - 从AdjustShotsDrawer接收到更新的行数据，行数:', updatedRows.length);

      if (!updatedRows || updatedRows.length === 0) {
        console.warn('接收到的行数据为空');
        return;
      }

      // 直接使用从 AdjustShotsDrawer 返回的数据（已经是正确格式）
      // 只需要确保必要的属性存在
      const processedRows = updatedRows.map((row, index) => {
        const processedRow = {
          ...row,
          index: index + 1, // 重新编号
          isSelected: false, // 重置选中状态
        };

        // 确保必要的属性存在
        if (!processedRow.tags || !Array.isArray(processedRow.tags)) {
          processedRow.tags = [];
        }

        if (!processedRow.selectedTags || !Array.isArray(processedRow.selectedTags)) {
          processedRow.selectedTags = [];
        }

        if (!processedRow.mergedRows || !Array.isArray(processedRow.mergedRows)) {
          processedRow.mergedRows = [];
        }

        if (processedRow.isMerged === undefined) {
          processedRow.isMerged = false;
        }

        // 确保 isSelected 属性存在
        if (processedRow.isSelected === undefined) {
          processedRow.isSelected = false;
        }

        return processedRow;
      });

      // 🆕 使用新的数据持久化系统更新合并状态
      this.dataPersistence.updateMergeState(this, processedRows);

      // 更新项目数据中的行
      if (this.projectData.data) {
        this.projectData.data.rows = this.rows;

        // 使用防抖保存项目数据
        console.log('[Performance] 保存更新后的项目数据到project.json...');
        this.debouncedSaveProject();

        // 立即显示成功消息，不等待保存完成
        const mergedRows = this.rows.filter(row => row.isMerged);
        const mergedGroupsText = mergedRows.length > 0 ? `，包含 ${mergedRows.length} 个合并组` : '';
        this.showSuccessMessage('分组操作完成', `已更新 ${this.rows.length} 行数据${mergedGroupsText}`);
      }
    },

    // 🆕 获取格式化的文本内容供全局推理使用 - 使用新的提示词推理管理系统
    getFormattedTextForReasoning() {
      return this.promptReasoningStudio.getFormattedTextForReasoning(this);
    },

    // 🆕 处理图像生成 - 使用新的图像生成管理系统
    async handleGenerateImage(index) {
      await this.imageGenerationStudio.handleGenerateImage(this, index);
    },

    // 🆕 为指定行生成图像 - 批量操作专用 - 使用新的图像生成管理系统
    async generateImageForRow(index) {
      return await this.imageGenerationStudio.generateImageForRow(this, index);
    },

    // 🆕 处理取消生成 - 使用新的图像生成管理系统
    async handleCancelGeneration(index) {
      await this.imageGenerationStudio.handleCancelGeneration(this, index);
    },



    // 🆕 批量取消功能 - 使用新的批量操作管理系统
    async performBatchCancel() {
      await this.batchOperationsStudio.performBatchCancel(this);
    },

    // 🆕 处理取消排队 - 使用新的批量操作管理系统
    async handleCancelQueue(index) {
      await this.batchOperationsStudio.handleCancelQueue(this, index);
    },

    // 🆕 处理清空队列 - 使用新的批量操作管理系统
    async handleClearQueue() {
      await this.batchOperationsStudio.handleClearQueue(this);
    },

    // 🆕 切换队列面板折叠状态已迁移到 useUIStateStudio composable

    // 🆕 调试队列状态 - 使用新的图像生成管理系统
    debugQueueState() {
      return this.imageGenerationStudio.debugQueueState(this);
    },



    // 🆕 处理单张图片生成完成 - 使用新的图像生成管理系统
    handleSingleImageGenerated(rowIndex, imageData) {
      this.imageGenerationStudio.handleSingleImageGenerated(this, rowIndex, imageData);
    },

    // 🆕 立即保存项目数据（不使用防抖） - 使用新的数据管理系统
    async saveProjectDataImmediately() {
      await this.dataManagementStudio.saveProjectDataImmediately(this);
    },



    // 🆕 简化的图片数据验证 - 使用新的数据管理系统
    simpleImageDataValidation() {
      return this.dataManagementStudio.simpleImageDataValidation(this);
    },

    // 🆕 清理generatedImages冗余数据 - 使用新的数据验证工具
    cleanupGeneratedImagesData() {
      // 使用新的数据验证工具
      const result = this.dataValidationUtils.cleanupRedundantData(this.rows);

      if (result.cleanedCount > 0) {
        // 更新项目数据
        if (this.projectData && this.projectData.data) {
          this.projectData.data.rows = this.rows;
        }

        // 保存清理后的数据
        this.debouncedSaveProject();

        this.showSuccessMessage('数据清理完成', `已清理${result.cleanedCount}行的冗余generatedImages数据，数据结构已简化`);
        return result.cleanedCount;
      } else {
        this.showInfoMessage('数据清理', '没有发现需要清理的generatedImages数据');
        return 0;
      }
    },

    // 错误恢复相关方法已移除，改用toast通知

    // 🆕 设置全局toast通知系统
    setupGlobalToastSystem() {
      // 将showToast方法绑定到window对象，供其他模块使用
      window.showToast = (options) => {
        console.log('🔔 [Toast] 全局toast被调用:', options);

        // 🔍 详细验证参数
        if (!options) {
          console.warn('🔔 [Toast] options参数为空，跳过显示');
          return;
        }

        if (!options.title && !options.message) {
          console.warn('🔔 [Toast] title和message都为空，跳过显示:', {
            title: options.title,
            message: options.message,
            hasTitle: !!options.title,
            hasMessage: !!options.message
          });
          return;
        }

        // 确保至少有一个有效的显示内容
        const finalTitle = options.title || '通知';
        const finalMessage = options.message || '无消息内容';

        console.log('🔔 [Toast] 调用本地showToast方法:', {
          type: options.type || 'info',
          title: finalTitle,
          message: finalMessage
        });

        this.showToast(
          options.type || 'info',
          finalTitle,
          finalMessage
        );
      };

      // 🆕 设置全局调试方法 - 使用新的调试工具
      this.debugUtils.setupGlobalMethods(this);






      // 🆕 测试toast系统是否正常工作
      console.log('🔔 [Toast] 全局toast通知系统已设置');
      console.log('🔍 [调试] 全局调试方法已设置');

      // 延迟测试，确保组件完全加载
      setTimeout(() => {
        if (typeof window.showToast === 'function') {
          console.log('✅ [Toast] 全局toast系统测试通过');
        } else {
          console.error('❌ [Toast] 全局toast系统设置失败');
        }
      }, 1000);
    },

    // 🆕 行锁定功能方法 - 使用新的行操作系统
    async handleToggleRowLock(rowIndex) {
      await this.rowOperations.toggleRowLock(this, rowIndex);
    },

    // 🆕 批量选择功能方法 - 使用新的行操作系统
    handleSelectAll() {
      this.rowOperations.selectAllRows(this);
    },

    // 🆕 清空选择功能方法 - 使用新的行操作系统
    handleClearSelection() {
      this.rowOperations.clearAllSelections(this);
    },

    // 🆕 显示锁定行提示消息 - 使用新的行操作系统
    showLockedRowMessage() {
      this.rowOperations.showLockedRowMessage(this);
    },

    // 🆕 修复图片数据结构一致性 - 使用新的数据管理系统
    fixImageDataStructure() {
      return this.dataManagementStudio.fixImageDataStructure(this);
    },

    // 🆕 调试验证数据保存流程 - 使用新的调试工具
    async debugDataSaveFlow() {
      // 使用新的调试工具
      return await this.debugUtils.debugSaveFlow(this);
    },

    // 🆕 初始化图像生成服务 - 缺失的方法
    async initializeImageGeneration() {
      await this.lifecycleStudio.initializeImageGeneration(this);
    },

    // 🆕 执行状态验证和重置 - 缺失的方法
    async performStateValidationAndReset() {
      await this.lifecycleStudio.performStateValidationAndReset(this);
    },

    // 🆕 确保角色数据结构正确 - 缺失的方法
    ensureCharacterDataStructure() {
      this.lifecycleStudio.ensureCharacterDataStructure(this);
    },


  },
  watch: {
    // Toast数据变化监听现在由 useToastNotification composable 管理

    // 监听项目数据变化并更新上下文 - 优化版
    projectData: {
      handler(newProjectData, oldProjectData) {
        // 性能优化：只在数据真正变化时才处理
        if (newProjectData === oldProjectData) {
          return;
        }

        console.log('[Performance] 项目数据变化，设置到上下文');
        if (newProjectData && this.projectContextTools) {
          console.log('[Performance] 行数据数量:', newProjectData?.data?.rows?.length || 0);

          // 批量更新上下文，减少触发次数
          this.projectContextTools.setProjectContext({
            projectData: newProjectData,
            projectTitle: newProjectData.title || '',
            chapterTitle: newProjectData.currentChapter || '',
            currentStep: this.currentStep
          });
        }

        // 当项目数据变化时，加载全局选择的角色状态
        if (newProjectData && newProjectData.data &&
            (!oldProjectData || !oldProjectData.data ||
             newProjectData.data.currentSelectedCharacters !== oldProjectData.data.currentSelectedCharacters)) {
          this.loadGlobalSelectedCharacters();
        }
      },
      deep: false, // 改为浅监听，提升性能
      immediate: true
    },

    // 监听当前步骤变化
    currentStep(newStep) {
      if (newStep && this.projectContextTools) {
        this.projectContextTools.setCurrentStep(newStep);
      }
    },
  }
}
</script>

<style scoped>
@import '../assets/styles/studio.css';

/* 添加无数据时的提示样式 */
.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 20px;
  background-color: #1a1a1a;
  border-radius: 8px;
  margin: 20px;
  text-align: center;
}

.message-icon {
  font-size: 40px;
  margin-bottom: 20px;
  color: #2dc0f0;
}

.message-text {
  font-size: 18px;
  color: #e0e0e0;
  margin-bottom: 10px;
}

.message-subtext {
  font-size: 14px;
  color: #999;
  margin-bottom: 10px;
}

/* 合并的操作栏样式 */
.combined-action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #1a1a1a;
  border-bottom: 1px solid #333;
  margin-bottom: 0.5rem;
}

.left-actions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.right-info {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.selection-info {
  font-size: 0.9rem;
  color: #cccccc;
  padding: 0.25rem 0.5rem;
  background-color: #2a2a2a;
  border-radius: 4px;
  border: 1px solid #444;
}

/* 强制刷新按钮样式 */
.refresh-btn {
  padding: 0.4rem 0.8rem;
  background-color: #2dc0f0;
  color: #121212;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  font-size: 0.85rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.refresh-btn:hover {
  background-color: #1aa8d6;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(45, 192, 240, 0.4);
}

.refresh-btn:active {
  transform: translateY(0);
  background-color: #1590ba;
}

.refresh-btn i {
  font-size: 14px;
}

/* 粘性表头样式 */
.table-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px); /* 根据页面布局调整高度 */
  overflow: hidden;
}

.table-header-container {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #1a1a1a;
  border-bottom: 2px solid #444;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.table-body-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: auto;
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #444 #222;
}

.table-body-container::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.table-body-container::-webkit-scrollbar-track {
  background: #222;
  border-radius: 6px;
}

.table-body-container::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 6px;
  border: 2px solid #222;
}

.table-body-container::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

.table-body-container::-webkit-scrollbar-corner {
  background: #222;
}

/* 添加手动加载按钮样式 */
.force-load-button {
  margin-top: 15px;
  padding: 8px 15px;
  background-color: #2dc0f0;
  color: #121212;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.force-load-button:hover {
  background-color: #1aa8d6;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(45, 192, 240, 0.4);
}

.force-load-button:active {
  transform: translateY(0);
  background-color: #1590ba;
}

.icon-info::before {
  content: "i";
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  background-color: #2dc0f0;
  color: #121212;
  font-style: italic;
  font-weight: bold;
}

.icon-warning::before {
  content: "!";
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  background-color: #ff9800;
  color: #121212;
  font-style: italic;
  font-weight: bold;
}

.icon-loading::before {
  content: "";
  display: inline-block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 4px solid rgba(45, 192, 240, 0.3);
  border-top-color: #2dc0f0;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 添加操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
  background-color: #1d1d2c;
  border-bottom: 1px solid #333355;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #2d94cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background-color: #3da3d9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(45, 148, 204, 0.3);
}

.refresh-btn:active {
  transform: translateY(0);
  background-color: #2280b3;
}

.refresh-btn i {
  font-size: 1.1rem;
}

/* 🆕 队列状态面板样式 - 改进版本 */
.queue-status-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(30, 30, 30, 0.95);
  border: 1px solid #f59e0b;
  border-radius: 8px;
  color: #fbbf24;
  font-size: 13px;
  /* 🔧 减少阴影强度，避免highlight出界 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  min-width: 200px;
  max-width: 280px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
  /* 🔧 确保内容不会溢出 */
  overflow: hidden;
}

.queue-status-panel.collapsed {
  /* 🔧 增加折叠状态的最小宽度，确保文字完整显示 */
  min-width: 140px;
  max-width: 160px;
}

.queue-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 8px 12px;
  margin: -8px -12px 8px -12px;
  font-weight: 600;
  color: #fbbf24;
  background-color: rgba(245, 158, 11, 0.1);
  border-radius: 6px 6px 0 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
  /* 🔧 确保内容不会溢出 */
  overflow: hidden;
}

/* 🔧 折叠状态下优化头部底部间距 */
.queue-status-panel.collapsed .queue-header {
  margin-bottom: 4px;
}

.queue-header:hover {
  /* 🔧 减少hover效果强度，避免highlight出界 */
  background-color: rgba(245, 158, 11, 0.12);
}

.collapse-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.collapse-icon.rotated {
  transform: rotate(180deg);
}

.queue-icon {
  font-size: 16px;
  /* 🔧 恢复脉冲动画效果 */
  animation: queue-pulse 2s ease-in-out infinite;
}

.queue-title {
  font-size: 14px;
  flex: 1;
}

.queue-content {
  padding: 0 12px 8px 12px;
  margin: 0 -12px -8px -12px;
}

.queue-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

/* 🆕 空队列状态样式 */
.queue-empty-state {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 0;
  color: #9ca3af;
  font-size: 13px;
  border-top: 1px solid rgba(156, 163, 175, 0.2);
  margin-top: 8px;
}

.queue-empty-state .empty-icon {
  font-size: 16px;
  color: #10b981;
}

.queue-empty-state .empty-text {
  font-style: italic;
}

.queue-summary {
  /* 🔧 优化折叠状态垂直布局 - 减少垂直空白 */
  padding: 4px 12px;
  margin: 0 -12px -4px -12px;
  text-align: center;
  font-size: 12px;
  color: #d97706;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 🔧 调整最小高度，更贴合文字内容 */
  min-height: 18px;
  overflow: hidden;
}

.summary-text {
  font-weight: 500;
  /* 🔧 确保文字完整显示并居中 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  /* 🔧 优化行高，实现更紧凑的布局 */
  line-height: 1.1;
}

.queue-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  color: #d97706;
  font-size: 12px;
}

.stat-value {
  color: #fbbf24;
  font-weight: 600;
  font-size: 12px;
}

.queue-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #374151;
}

.queue-action-btn {
  background-color: #dc2626;
  color: #ffffff;
  border: 1px solid #dc2626;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.queue-action-btn:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.queue-action-btn i {
  font-size: 12px;
}

/* 🔧 恢复队列脉冲动画定义 */
@keyframes queue-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}


</style>