/**
 * 项目上下文管理 Composable for ContentCreationStudio
 * 从 ContentCreationStudio.vue 中提取的项目上下文管理相关功能
 * 
 * 功能：
 * - 项目和章节管理
 * - URL参数处理
 * - 项目初始化
 * - 导航管理
 * - 项目状态管理
 */

import { reactive } from 'vue';

export function useProjectContextStudio() {
  // 响应式状态
  const projectState = reactive({
    localProjectTitle: '',
    localChapterTitle: '',
    currentStep: 'edit',
    projectData: {},
    serverBaseUrl: 'http://localhost:8091'
  });

  const navigationState = reactive({
    activeNavTab: 'match-tags',
    hasSrtFile: false,
    hasAudioFile: false,
    useBuiltinAudio: false
  });

  /**
   * 获取服务器基础URL
   * @returns {Promise<string>} 服务器URL
   */
  const getServerBaseUrl = async () => {
    try {
      // 尝试从环境变量或配置中获取
      const response = await fetch('/api/local/config');
      if (response.ok) {
        const config = await response.json();
        projectState.serverBaseUrl = config.serverUrl || 'http://localhost:8091';
      }
    } catch (error) {
      console.warn('无法获取服务器配置，使用默认URL:', error);
      projectState.serverBaseUrl = 'http://localhost:8091';
    }
    return projectState.serverBaseUrl;
  };

  /**
   * 构建API URL
   * @param {string} path - API路径
   * @returns {string} 完整的API URL
   */
  const getApiUrl = (path) => {
    return `${projectState.serverBaseUrl}/api/${path}`;
  };

  /**
   * 从URL获取项目参数
   * @returns {Object} 包含项目和章节信息的对象
   */
  const getProjectParamsFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      project: urlParams.get('project'),
      chapter: urlParams.get('chapter')
    };
  };

  /**
   * 更新URL参数
   * @param {string} projectTitle - 项目标题
   * @param {string} chapterTitle - 章节标题
   */
  const updateUrlParams = (projectTitle, chapterTitle) => {
    const url = new URL(window.location.href);
    url.searchParams.set('project', projectTitle);
    url.searchParams.set('chapter', chapterTitle);
    window.history.replaceState({}, '', url.toString());
  };

  /**
   * 初始化项目信息
   * @param {Object} context - 组件上下文
   * @param {Object} props - 组件props
   * @returns {Promise<Object>} 项目和章节信息
   */
  const initializeProjectInfo = async (context, props) => {
    // 尝试获取服务器基础URL
    await getServerBaseUrl();

    // 获取项目和章节信息，优先使用props
    let projectFromProps = props.projectTitle || '';
    let chapterFromProps = props.chapterTitle || '';

    // 如果props中没有，尝试从URL获取
    if (!projectFromProps || !chapterFromProps) {
      const urlParams = getProjectParamsFromUrl();
      projectFromProps = projectFromProps || urlParams.project;
      chapterFromProps = chapterFromProps || urlParams.chapter;
    }

    // 检查是否有合并操作的标志
    const hasMerged = projectFromProps && chapterFromProps ?
      context.dataPersistence.getMergeFlag(projectFromProps, chapterFromProps) : false;

    return {
      projectTitle: projectFromProps,
      chapterTitle: chapterFromProps,
      hasMerged
    };
  };

  /**
   * 更新项目标题
   * @param {Object} context - 组件上下文
   * @param {string} newTitle - 新标题
   */
  const updateProjectTitle = (context, newTitle) => {
    // 检查是否是章节标题更新
    if (projectState.localChapterTitle) {
      console.log('更新章节标题:', newTitle);
      projectState.localChapterTitle = newTitle;
      // 更新项目数据中的章节标题
      if (projectState.projectData.data) {
        projectState.projectData.data.currentChapter = newTitle;
      } else if (projectState.projectData) {
        projectState.projectData.currentChapter = newTitle;
      }
    } else {
      // 更新项目标题
      console.log('更新项目标题:', newTitle);
      projectState.localProjectTitle = newTitle;
      // 更新项目数据
      if (projectState.projectData) {
        projectState.projectData.title = newTitle;
      }
    }

    // 使用防抖保存项目
    if (projectState.projectData) {
      context.debouncedSaveProject();
    }
  };

  /**
   * 导航返回
   * @param {Object} context - 组件上下文
   */
  const goBack = (context) => {
    // 直接返回到主页
    console.log('ContentCreationStudio: 返回到主页(Homepage)');
    // 导航到home页面
    context.$emit('navigate', 'home');
  };

  /**
   * 前往下一步
   */
  const goToNextStep = () => {
    if (projectState.currentStep === 'edit') {
      projectState.currentStep = 'preview';
    }
  };

  /**
   * 导航到指定步骤
   * @param {Object} context - 组件上下文
   * @param {string} stepName - 步骤名称
   */
  const navigateToStep = (context, stepName) => {
    // 处理不同步骤的导航
    if (stepName === 'import') {
      // 如果点击"导入素材"，则返回CreationPage
      console.log('ContentCreationStudio: 点击导入素材，返回CreationPage');

      // 确保项目数据正确传递并且对项目数据进行深拷贝
      const projectDataToSend = JSON.parse(JSON.stringify(projectState.projectData));

      // 明确导航到creation页面，并传递项目数据
      context.$emit('navigate', 'creation', projectDataToSend);
    } else if (stepName === 'edit' || stepName === 'preview') {
      projectState.currentStep = stepName;
    }
  };

  /**
   * 处理导出导入
   * @param {Object} context - 组件上下文
   */
  const handleExportImport = (context) => {
    // 实现导出导入逻辑
    console.log('处理导出导入操作', context);
    // 这里可以添加具体的导出导入逻辑
  };

  /**
   * 处理标签页变化
   * @param {Object} context - 组件上下文
   * @param {string} tabName - 标签页名称
   */
  const handleTabChange = (context, tabName) => {
    navigationState.activeNavTab = tabName;

    // 🔧 同步到组件的activeNavTab状态
    if (context) {
      context.activeNavTab = tabName;
    }

    console.log('[Performance] 切换到标签:', tabName);

    // 🔧 根据标签页执行相应的功能操作
    switch (tabName) {
      case 'adjust-shots':
        handleAdjustShotsTab(context);
        break;
      case 'match-tags':
        handleGlobalReasoningTab(context);
        break;
      case 'manage-tags':
        console.log('🏷️ 切换到管理标签标签页');
        // 可以在这里添加管理标签相关的初始化逻辑
        break;
      case 'generate-intro':
        console.log('📝 切换到待定标签页');
        break;
      default:
        console.warn('未知的标签页:', tabName);
    }
  };

  /**
   * 处理调整分镜标签页
   * @param {Object} context - 组件上下文
   */
  const handleAdjustShotsTab = (context) => {
    console.log('[Performance] 打开调整分镜抽屉，准备发送行数据，行数:', context.rows?.length);

    if (!context.rows || context.rows.length === 0) {
      console.error('[Performance] 没有行数据可以发送');
      context.$emit('open-adjust-shots-drawer', []);
      return;
    }

    // 开始批量更新模式，避免在数据处理过程中触发保存
    if (context.startBatchUpdate) {
      context.startBatchUpdate();
    }

    try {
      console.log('[Performance] 发送行数据到抽屉，行数:', context.rows.length);

      // 确保行数据中包含所有必要信息，特别是合并相关的属性
      const rowsWithFullInfo = context.rows.map(row => {
        // 确保每行都有originalIndex
        if (row.originalIndex === undefined) {
          row.originalIndex = row.index;
        }

        // 确保每行都有isMerged属性
        if (row.isMerged === undefined) {
          row.isMerged = false;
        }

        // 确保合并行有mergedRows属性
        if (row.isMerged && !row.mergedRows) {
          row.mergedRows = [];
        }

        return row;
      });

      // 发出事件，传递处理后的行数据
      context.$emit('open-adjust-shots-drawer', rowsWithFullInfo);
    } finally {
      // 结束批量更新模式
      if (context.endBatchUpdate) {
        context.endBatchUpdate();
      }
    }
  };

  /**
   * 处理全局推理标签页
   * @param {Object} context - 组件上下文
   */
  const handleGlobalReasoningTab = (context) => {
    console.log('[Performance] 打开全局推理抽屉，准备发送行数据，行数:', context.rows?.length);

    if (!context.rows || context.rows.length === 0) {
      console.error('[Performance] 没有行数据可以发送');
      context.$emit('open-global-reasoning-drawer', []);
      return;
    }

    // 获取格式化的文本内容
    const formattedText = context.getFormattedTextForReasoning ? context.getFormattedTextForReasoning() : '';
    console.log('[Performance] 生成的格式化文本预览:',
      formattedText.length > 200 ? formattedText.substring(0, 200) + '...' : formattedText);

    // 确保每行数据都有text属性
    const enhancedRows = context.rows.map(row => {
      // 如果row没有text属性，使用description填充
      if (!row.text && row.description) {
        return { ...row, text: row.description };
      }
      return row;
    });

    // 发出事件，传递处理后的行数据和格式化文本
    context.$emit('open-global-reasoning-drawer', enhancedRows, formattedText);
  };

  /**
   * 处理导航操作
   * @param {Object} context - 组件上下文
   * @param {string} action - 操作名称
   */
  const handleNavAction = (context, action) => {
    console.log('🚀 [useProjectContextStudio] handleNavAction 被调用');
    console.log('🚀 [useProjectContextStudio] 导航操作:', action);
    console.log('🚀 [useProjectContextStudio] context.batchOperationUI 存在:', !!context.batchOperationUI);

    // 根据不同的操作执行相应的逻辑
    switch (action) {
      case 'refresh':
        // 刷新操作
        break;
      case 'settings':
        // 设置操作
        break;
      case 'smart-reasoning': {
        // 批量推理操作 - 检查当前状态决定是开始还是取消
        console.log('🧠 [导航] 批量推理按钮被点击');

        // 检查当前是否正在处理中
        const isCurrentlyProcessing = context.batchOperationUI?.batchInferenceState?.value?.isProcessing;

        if (isCurrentlyProcessing) {
          // 如果正在处理中，执行取消操作
          console.log('🚫 [导航] 检测到正在处理中，执行取消批量推理操作');
          if (context.performBatchInferenceCancel) {
            context.performBatchInferenceCancel();
          } else {
            console.error('🚫 [导航] performBatchInferenceCancel 方法不存在');
          }
        } else {
          // 如果未在处理中，执行批量推理操作
          console.log('🧠 [导航] 执行批量推理操作');
          if (context.performBatchInference) {
            context.performBatchInference();
          } else {
            console.error('🧠 [导航] performBatchInference 方法不存在');
          }
        }
        break;
      }
      case 'generate-images': {
        // 批量图像生成操作 - 检查当前状态决定是开始还是取消
        console.log('🎨 [导航] 批量生图按钮被点击');

        // 检查当前是否正在处理中
        const isCurrentlyGenerating = context.batchOperationUI?.batchImageGenerationState?.value?.isProcessing;

        if (isCurrentlyGenerating) {
          // 如果正在处理中，执行取消操作
          console.log('🚫 [导航] 检测到正在生图中，执行取消批量生图操作');
          if (context.performBatchCancel) {
            context.performBatchCancel();
          } else {
            console.error('🚫 [导航] performBatchCancel 方法不存在');
          }
        } else {
          // 如果未在处理中，执行批量图像生成操作
          console.log('🎨 [导航] 执行批量图像生成操作');
          if (context.performBatchImageGeneration) {
            context.performBatchImageGeneration();
          } else {
            console.error('🎨 [导航] performBatchImageGeneration 方法不存在');
          }
        }
        break;
      }
      default:
        console.warn('未知的导航操作:', action);
    }
  };

  /**
   * 从项目数据中提取章节标题
   * @param {Object} projectData - 项目数据
   * @returns {string} 章节标题
   */
  const extractChapterTitle = (projectData) => {
    // 获取章节标题
    if (projectData.data?.currentChapter) {
      return projectData.data.currentChapter;
    } else if (projectData.currentChapter) {
      return projectData.currentChapter;
    } else {
      // 尝试从文件路径中提取章节名称
      const srtPath = projectData.data?.srtFilePath;
      if (srtPath) {
        const pathParts = srtPath.split('/');
        if (pathParts.length >= 3) {
          // 假设路径格式为 draft/项目名/章节名/文件名
          return pathParts[2];
        }
      }
    }
    return '';
  };

  /**
   * 设置项目数据
   * @param {Object} projectData - 项目数据
   */
  const setProjectData = (projectData) => {
    projectState.projectData = projectData;
    
    if (projectData) {
      // 更新本地标题
      projectState.localProjectTitle = projectData.title || '';
      projectState.localChapterTitle = extractChapterTitle(projectData);
    }
  };

  /**
   * 更新项目上下文
   * @param {Object} context - 组件上下文
   * @param {Object} newProjectData - 新的项目数据
   */
  const updateProjectContext = (context, newProjectData) => {
    if (newProjectData && context.projectContextTools) {
      console.log('[Performance] 项目数据变化，设置到上下文');
      console.log('[Performance] 行数据数量:', newProjectData?.data?.rows?.length || 0);

      // 批量更新上下文，减少触发次数
      context.projectContextTools.setProjectContext({
        projectData: newProjectData,
        projectTitle: newProjectData.title || '',
        chapterTitle: newProjectData.currentChapter || '',
        currentStep: projectState.currentStep
      });
    }
  };

  return {
    // 响应式状态
    projectState,
    navigationState,

    // 服务器相关
    getServerBaseUrl,
    getApiUrl,

    // URL参数处理
    getProjectParamsFromUrl,
    updateUrlParams,

    // 项目初始化
    initializeProjectInfo,

    // 项目管理
    updateProjectTitle,
    setProjectData,
    extractChapterTitle,
    updateProjectContext,

    // 导航管理
    goBack,
    goToNextStep,
    navigateToStep,
    handleExportImport,
    handleTabChange,
    handleNavAction,

    // 标签页处理辅助方法
    handleAdjustShotsTab,
    handleGlobalReasoningTab
  };
}
