/**
 * 批量操作UI管理模块
 * 处理批量推理和批量生图的用户界面交互逻辑
 */

import { ref, computed } from 'vue'

export function useBatchOperationUI() {
  // 状态管理
  const batchInferenceState = ref({
    isProcessing: false,
    canCancel: false,
    buttonText: '1. 批量推理',
    progress: 0,
    currentTask: null
  })

  const batchImageGenerationState = ref({
    isProcessing: false,
    canCancel: false,
    buttonText: '2. 批量生图',
    progress: 0,
    currentTask: null
  })

  // 计算属性
  const isBatchInferenceProcessing = computed(() => batchInferenceState.value.isProcessing)
  const isBatchImageGenerationProcessing = computed(() => batchImageGenerationState.value.isProcessing)
  const isAnyBatchProcessing = computed(() => 
    isBatchInferenceProcessing.value || isBatchImageGenerationProcessing.value
  )

  /**
   * 验证用户选择
   * @param {Array} selectedRows - 选中的行
   * @param {Function} showToast - 显示Toast消息的函数
   * @param {string} operationType - 操作类型 ('inference' | 'generation')
   * @returns {Object} 验证结果
   */
  const validateUserSelection = (selectedRows, showToast, operationType = 'inference') => {
    console.log(`🔍 [批量操作验证] 验证${operationType}操作的用户选择`);

    // 检查是否有选中的行
    if (!selectedRows || selectedRows.length === 0) {
      const message = '请先选择需要处理的行';
      console.warn(`🔍 [批量操作验证] ${operationType}验证失败: ${message}`);
      
      if (showToast) {
        console.log('🔔 [Toast调试] 准备调用showToast函数:', {
          type: 'warning',
          title: '请选择行',
          message: message,
          showToastType: typeof showToast
        });

        showToast({
          type: 'warning',
          title: '请选择行',
          message: message,
          duration: 3000
        });

        console.log('🔔 [Toast调试] showToast函数调用完成');
      } else {
        console.warn('🔔 [Toast调试] showToast函数未提供或为空');
      }
      
      return {
        isValid: false,
        message: message,
        validRows: [],
        invalidRows: selectedRows || []
      };
    }

    let validRows = [];
    let invalidRows = [];
    let validationMessage = '';

    if (operationType === 'inference') {
      // 验证推理操作：检查是否有描述内容
      validRows = selectedRows.filter(row => row.description && row.description.trim() !== '');
      invalidRows = selectedRows.filter(row => !row.description || row.description.trim() === '');
      
      if (validRows.length === 0) {
        validationMessage = '选中的行中没有可用的描述内容，请先添加描述';
      }
    } else if (operationType === 'generation') {
      // 验证生图操作：检查是否有关键词
      validRows = selectedRows.filter(row => row.keywords && row.keywords.trim() !== '');
      invalidRows = selectedRows.filter(row => !row.keywords || row.keywords.trim() === '');
      
      if (validRows.length === 0) {
        validationMessage = '选中的行中没有可用的关键词，请先添加关键词或运行批量推理';
      }
    }

    const isValid = validRows.length > 0;

    if (!isValid && showToast) {
      console.log('🔔 [Toast调试] 准备调用showToast函数（数据验证失败）:', {
        type: 'warning',
        title: '数据验证失败',
        message: validationMessage,
        showToastType: typeof showToast
      });

      showToast({
        type: 'warning',
        title: '数据验证失败',
        message: validationMessage,
        duration: 4000
      });

      console.log('🔔 [Toast调试] showToast函数调用完成（数据验证失败）');
    } else if (!isValid) {
      console.warn('🔔 [Toast调试] 数据验证失败但showToast函数未提供');
    }

    console.log(`🔍 [批量操作验证] ${operationType}验证结果:`, {
      isValid,
      totalSelected: selectedRows.length,
      validCount: validRows.length,
      invalidCount: invalidRows.length,
      message: validationMessage
    });

    return {
      isValid,
      message: validationMessage,
      validRows,
      invalidRows,
      totalSelected: selectedRows.length,
      validCount: validRows.length,
      invalidCount: invalidRows.length
    };
  };

  /**
   * 开始批量推理操作
   * @param {Object} options - 操作选项
   */
  const startBatchInference = (options = {}) => {
    console.log('🧠 [批量推理UI] 开始批量推理操作');

    const newState = {
      isProcessing: true,
      canCancel: true,
      buttonText: '取消推理',
      progress: 0,
      currentTask: options.taskInfo || null
    };

    console.log('🧠 [批量推理UI] 设置新状态:', newState);
    batchInferenceState.value = newState;

    // 🔧 验证状态是否正确设置
    console.log('🧠 [批量推理UI] 状态设置后验证:', {
      current: batchInferenceState.value,
      isProcessing: batchInferenceState.value.isProcessing,
      buttonText: batchInferenceState.value.buttonText
    });

    // 触发开始事件
    if (options.onStart) {
      options.onStart();
    }
  };

  /**
   * 完成批量推理操作
   * @param {Object} result - 操作结果
   */
  const completeBatchInference = (result = {}) => {
    console.log('🧠 [批量推理UI] 批量推理操作完成:', result);

    batchInferenceState.value = {
      isProcessing: false,
      canCancel: false,
      buttonText: '1. 批量推理',
      progress: 0,
      currentTask: null
    };

    // 触发完成事件
    if (result.onComplete) {
      result.onComplete(result);
    }
  };

  /**
   * 开始批量图像生成操作
   * @param {Object} options - 操作选项
   */
  const startBatchImageGeneration = (options = {}) => {
    console.log('🎨 [批量生图UI] 开始批量图像生成操作');
    
    batchImageGenerationState.value = {
      isProcessing: true,
      canCancel: true,
      buttonText: '取消生图',
      progress: 0,
      currentTask: options.taskInfo || null
    };

    // 触发开始事件
    if (options.onStart) {
      options.onStart();
    }
  };



  /**
   * 完成批量图像生成操作
   * @param {Object} result - 操作结果
   */
  const completeBatchImageGeneration = (result = {}) => {
    console.log('🎨 [批量生图UI] 批量图像生成操作完成:', result);
    
    batchImageGenerationState.value = {
      isProcessing: false,
      canCancel: false,
      buttonText: '2. 批量生图',
      progress: 100,
      currentTask: null
    };

    // 触发完成事件
    if (result.onComplete) {
      result.onComplete(result);
    }
  };

  /**
   * 取消批量操作
   * @param {string} operationType - 操作类型 ('inference' | 'generation')
   * @param {Function} cancelFunction - 取消函数
   */
  const cancelBatchOperation = async (operationType, cancelFunction) => {
    console.log(`🚫 [批量操作UI] 取消${operationType}操作`);

    try {
      if (cancelFunction) {
        await cancelFunction();
      }

      // 重置对应的状态
      if (operationType === 'inference') {
        completeBatchInference({ cancelled: true });
      } else if (operationType === 'generation') {
        completeBatchImageGeneration({ cancelled: true });
      }

      console.log(`🚫 [批量操作UI] ${operationType}操作已取消`);
    } catch (error) {
      console.error(`🚫 [批量操作UI] 取消${operationType}操作失败:`, error);
    }
  };

  /**
   * 重置所有状态
   */
  const resetAllStates = () => {
    console.log('🔄 [批量操作UI] 重置所有状态');
    
    batchInferenceState.value = {
      isProcessing: false,
      canCancel: false,
      buttonText: '1. 批量推理',
      progress: 0,
      currentTask: null
    };

    batchImageGenerationState.value = {
      isProcessing: false,
      canCancel: false,
      buttonText: '2. 批量生图',
      progress: 0,
      currentTask: null
    };
  };

  return {
    // 状态
    batchInferenceState,
    batchImageGenerationState,
    
    // 计算属性
    isBatchInferenceProcessing,
    isBatchImageGenerationProcessing,
    isAnyBatchProcessing,
    
    // 方法
    validateUserSelection,
    startBatchInference,
    completeBatchInference,
    startBatchImageGeneration,
    completeBatchImageGeneration,
    cancelBatchOperation,
    resetAllStates
  };
}
