/**
 * 图像生成管理 Composable for ContentCreationStudio
 * 从 ContentCreationStudio.vue 中提取的图像生成相关功能
 * 
 * 功能：
 * - 单行图像生成
 * - 图像生成取消
 * - 图像选择和删除
 * - 缩略图管理
 * - 图像数据处理
 * - 实时图像放置
 * - 队列状态调试
 */

import { reactive } from 'vue';

export function useImageGenerationStudio() {
  // 图像生成状态管理
  const generationState = reactive({
    isProcessing: false,
    currentRowIndex: -1,
    lastGenerationResult: null,
    debugInfo: null
  });

  // 🔧 恢复生成历史功能
  const generationHistory = new Map();

  /**
   * 处理单行图像生成
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const handleGenerateImage = async (context, index) => {
    console.log('🎨 [图像生成] 开始为行生成图像:', index);

    try {
      // 检查行索引是否有效
      if (index < 0 || index >= context.rows.length) {
        throw new Error(`无效的行索引: ${index}`);
      }

      const row = context.rows[index];

      // 检查行是否被锁定
      if (row.isLocked) {
        context.showErrorMessage(
          '该行已锁定，无法生成图像。请先解锁该行再进行操作。',
          '生成操作被阻止'
        );
        return;
      }

      // 检查是否有关键词
      if (!row.keywords || row.keywords.trim() === '') {
        context.showErrorMessage('请先为该行输入关键词');
        return;
      }

      // 检查ComfyUI服务是否可用
      if (!context.imageGeneration) {
        context.showErrorMessage('图像生成服务未初始化，请刷新页面重试');
        return;
      }

      // 检查服务配置是否完整
      const serviceStatus = context.imageGeneration.getStatus();
      if (!serviceStatus.isAvailable) {
        context.showErrorMessage('ComfyUI服务不可用，请检查设置中的服务器地址和工作流配置');
        return;
      }

      // 🔧 关键修复：不要在UI层预设生成状态，让应用层的队列逻辑来决定
      // 只设置UI层的处理状态，不设置isGenerating
      generationState.isProcessing = true;
      generationState.currentRowIndex = index;

      // 🔧 修复：初始化行状态，但不设置isGenerating
      row.generationProgress = 0;
      row.generationMessage = '准备生成...';

      // 获取项目信息
      const projectTitle = context.localProjectTitle || context.projectData?.title || '未命名项目';
      const chapterTitle = context.localChapterTitle || context.projectData?.currentChapter || '未命名章节';

      console.log('🎨 [图像生成] 项目信息:', { projectTitle, chapterTitle });

      // 确保缩略图数组存在
      if (!row.thumbnails) {
        row.thumbnails = [];
      }

      // 🔧 关键修复：调用图像生成服务，添加完整的进度回调
      const result = await context.imageGeneration.generateImageForRow({
        prompt: row.keywords,
        negativePrompt: '', // 可以从设置中获取默认负向提示词
        projectTitle,
        chapterTitle,
        rowIndex: index,
        onImageGenerated: (imageData) => {
          // 实时图片放置回调
          // 🔧 修复：从imageData中获取rowIndex，如果没有则使用当前index
          const targetRowIndex = imageData.rowIndex !== undefined ? imageData.rowIndex : index;
          handleSingleImageGenerated(context, targetRowIndex, imageData);
        },
        onProgress: (progressData) => {
          // 🔧 修复：添加进度回调来正确更新行状态
          console.log('🔧 [图像生成] 进度更新:', {
            rowIndex: index,
            stage: progressData.stage,
            progress: progressData.progress,
            isQueued: progressData.isQueued,
            isProcessing: progressData.isProcessing,
            isCompleted: progressData.isCompleted
          });

          // 更新行状态
          if (progressData.isQueued) {
            row.isQueued = true;
            row.queueTaskId = progressData.taskId;
            row.queuePosition = progressData.queuePosition;
            row.generationMessage = progressData.message || '排队中...';
            row.isGenerating = false;
          } else if (progressData.isProcessing) {
            row.isQueued = false;
            row.isGenerating = true;
            row.generationMessage = progressData.message || '生成中...';
          } else if (progressData.isCompleted) {
            row.isQueued = false;
            row.isGenerating = false;
            row.generationMessage = '';
            row.queueTaskId = '';
            row.queuePosition = 0;
          }
        },
        onProgress: (progressData) => {
          // 🔧 实时进度更新回调 - 支持队列状态切换
          if (index >= 0 && index < context.rows.length) {
            const currentRow = context.rows[index];

            // 🔧 关键修复：检查取消标志，防止进度回调覆盖取消状态
            if (currentRow._isCancelling) {
              console.log('🔧 [图像生成] 检测到取消标志，忽略进度更新:', {
                rowIndex: index,
                stage: progressData.stage
              });
              return; // 直接返回，不更新任何状态
            }

            // 🔧 关键修复：检查是否是取消/中断状态
            if (progressData.isCancelled || progressData.stage === 'interrupted') {
              console.log('🔧 [图像生成] 检测到取消/中断状态，确保状态重置:', {
                rowIndex: index,
                stage: progressData.stage,
                isCancelled: progressData.isCancelled
              });

              // 🔧 关键修复：取消状态下强制重置，不允许其他状态覆盖
              currentRow.isGenerating = false;
              currentRow.isQueued = false;
              currentRow.generationProgress = 0;
              currentRow.generationMessage = progressData.message || '已取消';
              currentRow._isCancelling = true; // 设置取消标志

              // 清理队列相关状态
              if (currentRow.queueTaskId) {
                currentRow.queueTaskId = '';
                currentRow.queuePosition = 0;
              }
              return; // 🔧 关键：取消状态下直接返回，不执行后续逻辑
            }

            // 🔧 关键修复：根据进度状态更新行状态
            if (progressData.isCompleted) {
              console.log('🔧 [图像生成] 检测到完成状态，重置行状态:', {
                rowIndex: index,
                stage: progressData.stage,
                isError: progressData.isError
              });

              // 重置行的生成状态
              currentRow.isGenerating = false;
              currentRow.isQueued = false;
              currentRow.generationProgress = progressData.isError ? 0 : 100;
              currentRow.generationMessage = progressData.message || (progressData.isError ? '生成失败' : '生成完成');

              // 清理队列相关状态
              if (currentRow.queueTaskId) {
                currentRow.queueTaskId = '';
                currentRow.queuePosition = 0;
              }
            } else {
              // 🔧 关键修复：只有在非取消状态下才更新进度状态
              if (!progressData.isCancelled && progressData.stage !== 'interrupted') {
                currentRow.generationProgress = progressData.progress || 0;
                currentRow.generationMessage = progressData.message || '';
                currentRow.isGenerating = progressData.isProcessing || false;
              }
            }

            // 🔧 关键修复：根据应用层返回的状态设置UI状态
            if (progressData.isQueued) {
              // 任务进入队列
              console.log(`🔧 [状态切换] 行${index + 1} 进入排队状态`);
              currentRow.isQueued = true;
              currentRow.isGenerating = false;
              currentRow.queueTaskId = progressData.taskId || '';
              currentRow.queuePosition = progressData.queuePosition || 0;
              currentRow.generationMessage = `排队中 (${progressData.queuePosition || 0})`;
            } else if (progressData.isProcessing && currentRow.isQueued) {
              // 队列任务开始处理
              console.log(`🔧 [状态切换] 行${index + 1} 从"排队中"切换到"生成中"`);
              currentRow.isQueued = false;
              currentRow.isGenerating = true;
              currentRow.queueTaskId = '';
              currentRow.queuePosition = 0;
            } else if (progressData.isProcessing) {
              // 直接执行的任务
              currentRow.isGenerating = true;
              currentRow.isQueued = false;
            }

            // 🔧 关键修复：当任务完成时，清理UI状态
            if (progressData.isCompleted) {
              console.log(`🔧 [状态切换] 行${index + 1} 任务完成，清理生成状态`);

              // 🔧 清理所有生成相关状态
              currentRow.isGenerating = false;
              currentRow.isQueued = false;
              currentRow.generationProgress = 0;
              currentRow.generationMessage = '';
              currentRow.queueTaskId = '';
              currentRow.queuePosition = 0;

              // 🔧 清理UI层的处理状态
              if (generationState.currentRowIndex === index) {
                generationState.isProcessing = false;
                generationState.currentRowIndex = -1;
              }

              console.log(`✅ [状态切换] 行${index + 1} 状态已完全清理`);
            }

            // 🔧 关键修复：检查是否是取消/中断状态，避免覆盖取消状态
            if (progressData.isCancelled || progressData.stage === 'interrupted') {
              console.log(`🔧 [进度回调] 第${index + 1}行检测到取消状态，保持取消状态不变`);
              return; // 直接返回，不更新任何状态
            }

            // 更新进度和消息（如果任务未完成）
            if (!progressData.isCompleted) {
              const progress = progressData.progress || progressData.percentage || 0;
              const message = currentRow.isBatchGeneration
                ? `批量生成 ${currentRow.batchIndex + 1}/${currentRow.batchTotal} - ${progressData.message || ''}`
                : progressData.message || '';

              // 🔧 直接赋值（Vue会自动检测变化）
              currentRow.generationProgress = progress;
              currentRow.generationMessage = message;
            } else {
              // 🔧 任务完成或失败时的处理
              console.log(`🔧 [进度回调] 第${index + 1}行任务完成，状态:`, {
                isCompleted: progressData.isCompleted,
                isError: progressData.isError,
                isCancelled: progressData.isCancelled
              });

              // 🔧 直接赋值并触发Vue响应式更新
              currentRow.isGenerating = false;
              currentRow.isQueued = false;
              currentRow.queueTaskId = '';
              currentRow.queuePosition = 0;
              currentRow.generationProgress = 0;

              // 🆕 自动取消选中状态（队列任务完成）
              if (currentRow.isSelected) {
                currentRow.isSelected = false;
                console.log(`🔧 [状态管理] 行${index + 1} 队列任务完成，自动取消选中状态`);
              }

              // 设置完成消息
              if (progressData.isError) {
                currentRow.generationMessage = progressData.message || '生成失败';
              } else if (progressData.isCancelled) {
                currentRow.generationMessage = '任务已取消';
              } else {
                currentRow.generationMessage = '生成完成';
              }

              // 🔧 强制触发Vue响应式更新
              if (typeof context.$forceUpdate === 'function') {
                context.$forceUpdate();
              } else if (typeof context.forceUpdate === 'function') {
                context.forceUpdate();
              }
            }

            console.log(`🎨 [图像生成] 第${index + 1}行进度更新:`, {
              stage: progressData.stage,
              progress: progressData.progress || progressData.percentage,
              message: progressData.message,
              isQueued: currentRow.isQueued,
              isGenerating: currentRow.isGenerating,
              isProcessing: progressData.isProcessing,
              isCompleted: progressData.isCompleted
            });
          }
        }
      });

      console.log('🎨 [图像生成] 生成完成:', result);

      // 保存生成结果
      generationState.lastGenerationResult = {
        success: true,
        rowIndex: index,
        result: result
      };

      // 如果是队列任务，显示队列信息
      if (result.isQueued) {
        // 🔧 直接赋值（Vue会自动检测变化）
        row.isQueued = true;
        row.queueTaskId = result.taskId;
        row.queuePosition = result.queuePosition;
        row.generationMessage = `队列中 (位置: ${result.queuePosition})`;
        row.isGenerating = false; // 队列任务不是立即生成

        context.showInfoMessage(
          '已加入队列',
          `第${index + 1}行图像生成任务已加入队列，当前位置: ${result.queuePosition}`
        );
      } else {
        // 直接执行的任务
        context.showSuccessMessage('图像生成完成', `第${index + 1}行图像生成成功`);
      }

      // 保存项目数据
      context.debouncedSaveProject();

      return result;

    } catch (error) {
      console.error('🎨 [图像生成] 生成失败:', error);
      context.showErrorMessage('图像生成失败: ' + error.message);

      // 🔧 关键修复：确保错误时也重置行状态
      if (index >= 0 && index < context.rows.length) {
        const currentRow = context.rows[index];
        currentRow.isGenerating = false;
        currentRow.isQueued = false;
        currentRow.generationProgress = 0;
        currentRow.generationMessage = '生成失败: ' + error.message;

        // 清理队列相关状态
        if (currentRow.queueTaskId) {
          currentRow.queueTaskId = '';
          currentRow.queuePosition = 0;
        }
      }

      // 保存错误结果
      generationState.lastGenerationResult = {
        success: false,
        rowIndex: index,
        error: error.message
      };

    } finally {
      // 清除生成状态（包括队列任务完成后的状态清理）
      if (index >= 0 && index < context.rows.length) {
        const row = context.rows[index];

        // 修复：无论是直接执行还是队列执行，完成后都要清理状态
        if (!row.isQueued) { // 只有在不是排队状态时才清理
          // 🔧 直接赋值（Vue会自动检测变化）
          row.isGenerating = false;
          row.generationProgress = 0;
          row.generationMessage = '';

          // 自动取消选中状态（任务完成）
          if (row.isSelected) {
            row.isSelected = false;
            console.log(`🔧 [状态管理] 行${index + 1} 任务完成，自动取消选中状态`);
          }

          console.log(`🔧 [状态清理] 行${index + 1} 生成状态已清理`);
        }
      }

      // 重置全局状态
      generationState.isProcessing = false;
      generationState.currentRowIndex = -1;
    }
  };

  /**
   * 处理取消图像生成
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const handleCancelGeneration = async (context, index) => {
    console.log('🚫 [图像生成] 取消生成任务，行索引:', index);

    try {
      // 检查行索引是否有效
      if (index < 0 || index >= context.rows.length) {
        console.error('🚫 [图像生成] 无效的行索引:', index);
        return;
      }

      const row = context.rows[index];

      // 检查是否正在生成
      if (!row.isGenerating) {
        console.warn('🚫 [图像生成] 该行没有正在进行的生成任务');
        return;
      }

      // 🔧 关键修复：设置取消标志，防止进度回调覆盖状态
      console.log('🔧 [取消状态] 设置取消标志并立即重置行状态');
      row._isCancelling = true; // 设置取消标志
      row.isGenerating = false;
      row.generationProgress = 0;
      row.generationMessage = '正在取消...';

      // 🔧 强制UI更新
      if (context.$forceUpdate) {
        context.$forceUpdate();
      }

      // 调用图像生成服务的取消方法
      const cancelResult = await context.imageGeneration.cancelGeneration();

      if (cancelResult && cancelResult.success) {
        console.log('✅ [图像生成] 任务取消成功');
        context.showInfoMessage('任务已取消', '图像生成任务已成功取消');

        // 🔧 确保状态完全重置
        row.isGenerating = false;
        row.generationProgress = 0;
        row.generationMessage = '已取消';

        // 🔧 关键修复：延迟清除取消标志，确保所有进度回调都被忽略
        setTimeout(() => {
          row._isCancelling = false;
        }, 500);

        // 自动取消选中状态
        if (row.isSelected) {
          row.isSelected = false;
          console.log(`🔧 [状态管理] 行${index + 1} 任务取消，自动取消选中状态`);
        }

      } else {
        console.warn('⚠️ [图像生成] 任务取消失败:', cancelResult?.details || '未知错误');
        context.showWarningMessage('取消失败', '无法完全取消当前任务，但已停止本地状态');

        // 即使取消失败，也要确保本地状态重置
        row.isGenerating = false;
        row.generationProgress = 0;
        row.generationMessage = '取消失败但已停止';

        // 🔧 关键修复：延迟清除取消标志
        setTimeout(() => {
          row._isCancelling = false;
        }, 500);

        // 自动取消选中状态
        if (row.isSelected) {
          row.isSelected = false;
          console.log(`🔧 [状态管理] 行${index + 1} 任务取消（失败但重置），自动取消选中状态`);
        }
      }

      // 🔧 关键修复：延迟再次确认状态重置，确保UI正确更新
      setTimeout(() => {
        if (index >= 0 && index < context.rows.length) {
          const currentRow = context.rows[index];
          if (currentRow.isGenerating) {
            console.log('🔧 [延迟修复] 检测到状态未正确重置，强制重置');
            currentRow.isGenerating = false;
            currentRow.generationProgress = 0;
            currentRow.generationMessage = '';

            // 再次强制UI更新
            if (context.$forceUpdate) {
              context.$forceUpdate();
            }
          }

          // 🔧 确保取消标志最终被清除
          if (currentRow._isCancelling) {
            console.log('🔧 [延迟修复] 清除取消标志');
            currentRow._isCancelling = false;
          }
        }
      }, 1000); // 延长到1秒，确保所有回调都处理完毕

    } catch (error) {
      console.error('❌ [图像生成] 取消任务时出错:', error);
      context.showErrorMessage('取消失败', error.message || '取消任务时发生错误');

      // 出错时也要重置本地状态
      if (index >= 0 && index < context.rows.length) {
        const row = context.rows[index];
        row.isGenerating = false;
        row.generationProgress = 0;
        row.generationMessage = '取消出错';
        row._isCancelling = false; // 清除取消标志

        // 强制UI更新
        if (context.$forceUpdate) {
          context.$forceUpdate();
        }
      }
    }
  };

  /**
   * 处理重绘图像
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const handleRedrawImage = (context, index) => {
    context.redrawImage();
    context.showInfoMessage('重绘中', `正在重新生成第${index+1}行的镜头图片...`);
  };

  /**
   * 处理选择主图像
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const handleSelectMainImage = (context, index) => {
    context.selectMainImage();
    context.showInfoMessage('功能提示', `正在处理第${index+1}行的图片选择...`);
  };

  /**
   * 下载并保存图像到本地
   * @param {Object} context - 组件上下文
   * @param {string} imageUrl - 图像URL
   * @param {string} filename - 文件名
   * @param {number} rowIndex - 行索引
   */
  const downloadAndSaveImage = async (context, imageUrl, filename, rowIndex) => {
    try {
      console.log('💾 [图像保存] 开始下载图像:', { imageUrl, filename, rowIndex });

      // 构建保存路径
      const projectTitle = context.localProjectTitle || context.projectData?.title || '未命名项目';
      const chapterTitle = context.localChapterTitle || context.projectData?.currentChapter || '未命名章节';

      // 🔧 修复：确保文件名有正确的扩展名
      let finalFilename = filename;
      if (!finalFilename.toLowerCase().endsWith('.png') &&
          !finalFilename.toLowerCase().endsWith('.jpg') &&
          !finalFilename.toLowerCase().endsWith('.jpeg')) {
        finalFilename = `${filename}.png`;
      }

      console.log('💾 [图像保存] 下载图像URL:', imageUrl);

      // 🔧 修复：先从ComfyUI下载图像，增加错误处理和重试机制
      let imageResponse;
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          imageResponse = await fetch(imageUrl, {
            method: 'GET',
            headers: {
              'Accept': 'image/*',
              'User-Agent': 'txt2video-client'
            },
            timeout: 30000 // 30秒超时
          });

          if (imageResponse.ok) {
            break; // 成功，跳出重试循环
          } else {
            console.warn(`💾 [图像保存] 下载尝试 ${retryCount + 1} 失败: ${imageResponse.status} ${imageResponse.statusText}`);
          }
        } catch (fetchError) {
          console.warn(`💾 [图像保存] 下载尝试 ${retryCount + 1} 出错:`, fetchError.message);
        }

        retryCount++;
        if (retryCount < maxRetries) {
          console.log(`💾 [图像保存] 等待 ${retryCount * 1000}ms 后重试...`);
          await new Promise(resolve => setTimeout(resolve, retryCount * 1000));
        }
      }

      if (!imageResponse?.ok) {
        throw new Error(`下载图像失败，已重试 ${maxRetries} 次: ${imageResponse?.status || 'unknown'} ${imageResponse?.statusText || 'unknown error'}`);
      }

      const imageBlob = await imageResponse.blob();
      console.log('💾 [图像保存] 图像下载成功，大小:', imageBlob.size, 'bytes');

      // 🔧 修复：使用正确的API端点和FormData格式
      const formData = new FormData();
      formData.append('file', imageBlob, finalFilename);
      formData.append('projectTitle', projectTitle);
      formData.append('chapterTitle', chapterTitle);
      formData.append('subfolder', 'Img'); // 🔧 修复：保存到Img子文件夹，匹配现有目录结构

      console.log('💾 [图像保存] 发送保存请求:', {
        filename: finalFilename,
        projectTitle,
        chapterTitle,
        blobSize: imageBlob.size
      });

      const response = await fetch('/api/local/save-generated-image', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ [图像保存] 图像保存成功:', result);
        return result;
      } else {
        const errorText = await response.text();
        console.error('❌ [图像保存] 图像保存失败:', {
          status: response.status,
          statusText: response.statusText,
          errorText
        });
        throw new Error(`保存失败: ${response.status} ${errorText}`);
      }
    } catch (error) {
      console.error('❌ [图像保存] 图像保存出错:', error);
      throw error;
    }
  };



  /**
   * 🎯 新方案：处理单张图片生成完成 - 直接移动到项目目录
   * @param {Object} context - 组件上下文
   * @param {number} rowIndex - 行索引
   * @param {Object} imageData - 图像数据
   */
  const handleSingleImageGenerated = async (context, rowIndex, imageData) => {
    console.log('🎨 [实时放置] 收到单张图片:', { rowIndex, imageData });

    try {
      // 检查行索引是否有效
      if (rowIndex < 0 || rowIndex >= context.rows.length) {
        console.error('🎨 [实时放置] 无效的行索引:', rowIndex);
        return;
      }

      const row = context.rows[rowIndex];

      // 确保缩略图数组存在
      if (!row.thumbnails) {
        row.thumbnails = [];
      }

      // 🔧 关键修复：确保图像URL正确构建
      let imageUrl = imageData.url || imageData.imageUrl;

      // 如果没有URL，使用正确的后端代理路径构建
      if (!imageUrl) {
        const params = new URLSearchParams({
          filename: imageData.filename,
          type: imageData.type || 'output',
          serverUrl: 'http://localhost:8188'
        });

        if (imageData.subfolder) {
          params.append('subfolder', imageData.subfolder);
        }

        imageUrl = `http://localhost:8091/api/local/comfyui/view?${params.toString()}`;
        console.log('🔧 [实时放置] 构建的图像URL:', imageUrl);
      }

      // 🔧 确保filename字段存在
      const filename = imageData.filename || imageData.name || `generated_${Date.now()}.png`;

      // 🎯 验证图像是否可访问
      console.log('🔍 [实时放置] 验证图像可访问性:', filename);
      console.log('🔍 [实时放置] 图像URL:', imageUrl);

      try {
        const testResponse = await fetch(imageUrl, {
          method: 'HEAD',
          timeout: 5000 // 5秒超时
        });

        if (!testResponse.ok) {
          console.warn(`⚠️ [实时放置] 图像不可访问，跳过添加: ${filename} (${testResponse.status})`);
          return; // 跳过不可访问的图像
        }

        console.log('✅ [实时放置] 图像验证通过:', filename);
      } catch (verifyError) {
        console.warn(`⚠️ [实时放置] 图像验证失败，跳过添加: ${filename}`, verifyError.message);
        return; // 跳过验证失败的图像
      }

      // 🔧 防重复：检查是否已经添加过相同的图像
      const isDuplicate = (row.imageSrc && row.imageSrc.includes(filename)) ||
        (row.thumbnails && row.thumbnails.some(thumb =>
          thumb.filename === filename ||
          thumb.src.includes(filename)
        ));

      if (isDuplicate) {
        console.log('ℹ️ [实时放置] 图像已存在，跳过重复添加:', filename);
        return;
      }

      console.log('🔧 [实时放置] 图像数据详情:', {
        filename: imageData.filename,
        subfolder: imageData.subfolder,
        type: imageData.type,
        hasUrl: !!imageData.url,
        hasImageUrl: !!imageData.imageUrl
      });

      // 🔧 改进的图片放置逻辑 - 主图优先原则
      const isMainImageEmpty = !row.imageSrc ||
        row.imageSrc === 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7' ||
        row.imageSrc === '';

      if (isMainImageEmpty) {
        // 如果没有主图，第一张图片放入"本镜配图"区域
        row.imageSrc = imageUrl;
        row.imageAlt = `生成的图像 - ${row.keywords.substring(0, 50)}`;
        console.log('🎨 [实时放置] 图片已放入本镜配图区域:', imageUrl);
        console.log('🎨 [实时放置] 主图设置完成，imageSrc:', row.imageSrc);
      } else {
        // 如果已有主图，新图片添加到缩略图数组
        const thumbnailData = {
          src: imageUrl,
          alt: filename || `生成图片${row.thumbnails.length + 1}`,
          ismain: false
        };

        // 添加缩略图到数组
        row.thumbnails.push(thumbnailData);
        console.log('🎨 [实时放置] 图片已放入可选图区域:', imageUrl);
        console.log('🎨 [实时放置] 缩略图数组长度:', row.thumbnails.length);
      }

      // 🔧 新增：尝试下载并保存图像到本地（异步执行，不阻塞UI）
      downloadAndSaveImage(context, imageUrl, filename, rowIndex)
        .then((saveResult) => {
          console.log('✅ [实时放置] 图像保存成功:', saveResult);

          // 🎯 关键修复：更新界面中的图像路径为项目路径
          if (saveResult && saveResult.success && saveResult.path) {
            const projectImageUrl = `/api/local/get-image?path=${encodeURIComponent(saveResult.path)}`;

            // 🔧 修正逻辑：根据当前图像的实际位置更新路径
            // 检查当前图像是在主图位置还是缩略图位置
            const currentImageUrl = imageUrl; // 当前使用的ComfyUI URL

            // 检查主图是否是当前图像
            if (row.imageSrc === currentImageUrl) {
              // 更新主图为项目路径
              row.imageSrc = projectImageUrl;
              console.log('🔄 [路径更新] 主图已更新为项目路径:', projectImageUrl);
            }

            // 检查缩略图中是否有当前图像
            if (row.thumbnails && row.thumbnails.length > 0) {
              const thumbnailIndex = row.thumbnails.findIndex(thumb =>
                thumb.src === currentImageUrl ||
                thumb.filename === filename ||
                thumb.alt === filename ||
                thumb.src.includes(filename)
              );

              if (thumbnailIndex !== -1) {
                // 🎯 只更新必要字段，保持数据结构简洁
                row.thumbnails[thumbnailIndex].src = projectImageUrl;
                // 移除多余字段
                delete row.thumbnails[thumbnailIndex].filename;
                delete row.thumbnails[thumbnailIndex].timestamp;
                delete row.thumbnails[thumbnailIndex].isLocal;
                delete row.thumbnails[thumbnailIndex].isTemporary;
                console.log(`🔄 [路径更新] 缩略图${thumbnailIndex + 1}已更新为项目路径:`, projectImageUrl);
              }
            }
          }

          // 🔧 恢复：保存生成历史
          const projectTitle = context.localProjectTitle || context.projectData?.title || '未命名项目';
          const chapterTitle = context.localChapterTitle || context.projectData?.currentChapter || '未命名章节';
          const historyKey = `${projectTitle}/${chapterTitle}/${rowIndex}`;

          generationHistory.set(historyKey, {
            rowIndex,
            prompt: row.keywords,
            negativePrompt: '',
            images: [imageData],
            timestamp: new Date().toISOString(),
            projectTitle,
            chapterTitle,
            localPath: saveResult?.path,
            savedFilename: saveResult?.filename
          });

          console.log('📚 [生成历史] 已保存生成记录:', historyKey);

          // 🔧 新增：强制触发Vue响应式更新
          if (typeof context.$forceUpdate === 'function') {
            context.$forceUpdate();
          }

          // 🔧 新增：立即保存项目数据，确保图像信息被持久化
          if (typeof context.saveProjectDataImmediately === 'function') {
            context.saveProjectDataImmediately();
          } else if (typeof context.debouncedSaveProject === 'function') {
            context.debouncedSaveProject();
          }
        })
        .catch((error) => {
          console.warn('⚠️ [实时放置] 图像保存失败，但不影响显示:', error.message);
          // 即使保存失败，也要尝试保存项目数据
          if (typeof context.saveProjectDataImmediately === 'function') {
            context.saveProjectDataImmediately();
          }
        });

      console.log('🎨 [实时放置] 当前缩略图数组:', row.thumbnails);
      console.log('🎨 [实时放置] 当前主图:', row.imageSrc);

      // 调用简化的验证方法
      context.simpleImageDataValidation();

      // 立即保存项目数据
      context.saveProjectDataImmediately();

      // 显示进度消息
      const totalImages = (row.imageSrc && row.imageSrc !== 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7' ? 1 : 0) + (row.thumbnails ? row.thumbnails.length : 0);
      context.showInfoMessage('图片生成进度', `第${rowIndex + 1}行已生成${totalImages}张图片`);

    } catch (error) {
      console.error('🎨 [实时放置] 处理单张图片失败:', error);
    }
  };

  /**
   * 为指定行生成图像 - 批量操作专用
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const generateImageForRow = async (context, index) => {
    console.log('🎨 [批量生图] 为行生成图像:', index);

    try {
      // 检查行索引是否有效
      if (index < 0 || index >= context.rows.length) {
        throw new Error(`无效的行索引: ${index}`);
      }

      const row = context.rows[index];

      // 检查行是否被锁定
      if (row.isLocked) {
        throw new Error('该行已锁定，无法生成图像');
      }

      // 检查是否有关键词
      if (!row.keywords || row.keywords.trim() === '') {
        throw new Error('该行没有关键词');
      }

      // 检查ComfyUI服务是否可用
      if (!context.imageGeneration) {
        throw new Error('图像生成服务未初始化');
      }

      // 获取项目信息
      const projectTitle = context.localProjectTitle || context.projectData?.title || '未命名项目';
      const chapterTitle = context.localChapterTitle || context.projectData?.currentChapter || '未命名章节';

      // 确保缩略图数组存在
      if (!row.thumbnails) {
        row.thumbnails = [];
      }

      // 🔧 修复：调用图像生成服务，添加完整的进度回调
      const result = await context.imageGeneration.generateImageForRow({
        prompt: row.keywords,
        negativePrompt: '',
        projectTitle,
        chapterTitle,
        rowIndex: index,
        onImageGenerated: (imageData) => {
          // 实时图片放置回调
          // 🔧 修复：从imageData中获取rowIndex，如果没有则使用当前index
          const targetRowIndex = imageData.rowIndex !== undefined ? imageData.rowIndex : index;
          handleSingleImageGenerated(context, targetRowIndex, imageData);
        },
        onProgress: (progressData) => {
          // 🔧 修复：批量生图时的进度回调
          console.log('🔧 [批量生图] 进度更新:', {
            rowIndex: index,
            stage: progressData.stage,
            progress: progressData.progress,
            isQueued: progressData.isQueued,
            isProcessing: progressData.isProcessing,
            isCompleted: progressData.isCompleted
          });

          // 更新行状态
          if (progressData.isQueued) {
            row.isQueued = true;
            row.queueTaskId = progressData.taskId;
            row.queuePosition = progressData.queuePosition;
            row.generationMessage = progressData.message || '排队中...';
            row.isGenerating = false;
          } else if (progressData.isProcessing) {
            row.isQueued = false;
            row.isGenerating = true;
            row.generationMessage = progressData.message || '生成中...';
          } else if (progressData.isCompleted) {
            row.isQueued = false;
            row.isGenerating = false;
            row.generationMessage = '';
            row.queueTaskId = '';
            row.queuePosition = 0;
          }
        }
      });

      console.log('🎨 [批量生图] 第', index + 1, '行生成完成:', result);
      return result;

    } catch (error) {
      console.error('🎨 [批量生图] 第', index + 1, '行生成失败:', error);
      throw error;
    }
  };

  /**
   * 处理删除主图像
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const handleDeleteMainImage = async (context, index) => {
    await context.imageManagement.deleteMainImage(context, index);
  };

  /**
   * 处理删除缩略图
   * @param {Object} context - 组件上下文
   * @param {number} rowIndex - 行索引
   * @param {number} thumbnailIndex - 缩略图索引
   */
  const handleDeleteThumbnail = async (context, rowIndex, thumbnailIndex) => {
    await context.imageManagement.deleteThumbnail(context, rowIndex, thumbnailIndex);
  };

  /**
   * 处理选择缩略图
   * @param {Object} context - 组件上下文
   * @param {number} rowIndex - 行索引
   * @param {number} thumbnailIndex - 缩略图索引
   */
  const handleSelectThumbnail = (context, rowIndex, thumbnailIndex) => {
    context.imageManagement.selectThumbnail(context, rowIndex, thumbnailIndex);
  };

  /**
   * 处理管理图片
   * @param {Object} context - 组件上下文
   * @param {number} index - 行索引
   */
  const handleManageImages = (context, index) => {
    context.imageManagement.openImageManagement(context, index);
  };

  /**
   * 关闭图片管理窗口
   * @param {Object} context - 组件上下文
   */
  const closeImageManagementModal = (context) => {
    context.imageManagement.closeImageManagement();
  };

  /**
   * 从模态窗口删除主图
   * @param {Object} context - 组件上下文
   */
  const handleDeleteMainImageFromModal = async (context) => {
    if (context.imageManagement.currentManageImageRowIndex.value >= 0) {
      await handleDeleteMainImage(context, context.imageManagement.currentManageImageRowIndex.value);
    }
  };

  /**
   * 从模态窗口删除可选图
   * @param {Object} context - 组件上下文
   * @param {number} thumbnailIndex - 缩略图索引
   */
  const handleDeleteOptionalImageFromModal = async (context, thumbnailIndex) => {
    if (context.imageManagement.currentManageImageRowIndex.value >= 0) {
      await handleDeleteThumbnail(context, context.imageManagement.currentManageImageRowIndex.value, thumbnailIndex);
    }
  };

  /**
   * 从模态窗口上传图片
   * @param {Object} context - 组件上下文
   * @param {Array} files - 文件数组
   */
  const handleUploadImagesFromModal = async (context, files) => {
    await context.imageManagement.uploadImagesFromModal(context, files);
  };

  /**
   * 从模态窗口处理上传错误
   * @param {Object} context - 组件上下文
   * @param {string} errorMessage - 错误消息
   */
  const handleUploadErrorFromModal = (context, errorMessage) => {
    context.imageManagement.handleUploadError(context, errorMessage);
  };

  /**
   * 从模态窗口替换主图
   * @param {Object} context - 组件上下文
   * @param {File} file - 文件对象
   */
  const handleReplaceMainImageFromModal = async (context, file) => {
    await context.imageManagement.replaceMainImageFromModal(context, file);
  };

  /**
   * 从模态窗口替换可选图
   * @param {Object} context - 组件上下文
   * @param {Object} params - 参数对象
   */
  const handleReplaceOptionalImageFromModal = async (context, params) => {
    await context.imageManagement.replaceOptionalImageFromModal(context, params);
  };

  /**
   * 调试队列状态
   * @param {Object} context - 组件上下文
   */
  const debugQueueState = (context) => {
    if (!context.imageGeneration) {
      console.log('🔍 [队列调试] imageGeneration 未初始化');
      return;
    }

    const debugInfo = {
      isGenerating: context.imageGeneration.isGenerating,
      currentTask: context.imageGeneration.currentTask,
      queueStats: context.imageGeneration.queueStats,
      taskQueue: context.imageGeneration.taskQueue,
      queueProcessingCount: context.queueProcessingCount,
      shouldShowQueuePanel: context.shouldShowQueuePanel
    };

    console.log('🔍 [队列调试] 当前状态:', debugInfo);

    // 保存调试信息
    generationState.debugInfo = debugInfo;

    return debugInfo;
  };

  return {
    // 状态
    generationState,
    generationHistory,

    // 图像生成方法
    handleGenerateImage,
    generateImageForRow,
    handleCancelGeneration,
    handleRedrawImage,
    handleSelectMainImage,

    // 图像管理方法
    handleDeleteMainImage,
    handleDeleteThumbnail,
    handleSelectThumbnail,
    handleManageImages,
    closeImageManagementModal,

    // 模态窗口方法
    handleDeleteMainImageFromModal,
    handleDeleteOptionalImageFromModal,
    handleUploadImagesFromModal,
    handleUploadErrorFromModal,
    handleReplaceMainImageFromModal,
    handleReplaceOptionalImageFromModal,

    // 图像处理方法
    handleSingleImageGenerated,
    downloadAndSaveImage,

    // 调试方法
    debugQueueState
  };
}
