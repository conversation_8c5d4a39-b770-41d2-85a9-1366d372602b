/**
 * 图像生成组合式API - 重构版本
 * 整合各个功能模块，提供统一的API接口
 */

import { onMounted } from 'vue';
import { useImageState } from './image-generation/useImageState.js';
import { useImageQueue } from './image-generation/useImageQueue.js';
import { useImageExecution } from './image-generation/useImageExecution.js';
import { useQueueProcessor } from './image-generation/useQueueProcessor.js';
import { useComfyUIErrorRecovery } from './useComfyUIErrorRecovery.js';
import comfyuiImageGeneration from '../services/comfyuiImageGeneration.js';

export function useImageGeneration() {
  // 🆕 集成错误恢复机制
  const errorRecovery = useComfyUIErrorRecovery();

  // 初始化各个功能模块
  const imageState = useImageState();
  const imageQueue = useImageQueue();
  const imageExecution = useImageExecution();
  const queueProcessor = useQueueProcessor(imageQueue, imageState, imageExecution);

  // 解构需要的状态和方法
  const {
    isGenerating,
    isCancelling,
    generationProgress,
    generationStage,
    generationMessage,
    generationError,
    currentTaskId,
    isServiceAvailable,
    generationHistory,
    canGenerate,
    serviceStatus,
    setIsGenerating,
    initializeService,
    handleError,
    resetGenerationState,
    forceResetState,
    completeStateReset,
    getRowGenerationHistory,
    clearGenerationHistory
  } = imageState;

  const {
    taskQueue,
    currentTask,
    queueStats,
    addToQueue,
    getRowQueueStatus,
    cancelQueueByRowIndex
  } = imageQueue;

  const {
    executeImageGeneration,
    generateImagesForRows,
    cancelGeneration
  } = imageExecution;

  const {
    clearQueueAndStop
  } = queueProcessor;

  /**
   * 为指定行生成图像 - 支持实时回调和队列管理
   */
  const generateImageForRow = async (params) => {
    const {
      prompt,
      negativePrompt = '',
      projectTitle,
      chapterTitle,
      rowIndex,
      onImageGenerated,
      onProgress
    } = params;

    console.log('🎨 [图像生成] 请求为行生成图像:', {
      rowIndex,
      promptLength: prompt.length,
      projectTitle,
      chapterTitle
    });

    console.log('🔍 [DEBUG] generateImageForRow调用时的状态:', {
      isGenerating: isGenerating.value,
      currentTask: currentTask.value,
      taskQueueLength: taskQueue.length,
      timestamp: new Date().toISOString()
    });

    // 🔧 验证输入参数
    if (!prompt || prompt.trim() === '') {
      const errorMsg = '关键词不能为空';
      console.error('❌ [图像生成] 参数验证失败:', errorMsg);
      if (onProgress) {
        onProgress({
          stage: 'error',
          progress: 0,
          message: errorMsg,
          isError: true,
          isCompleted: true,
          isProcessing: false
        });
      }
      throw new Error(errorMsg);
    }

    // 🔧 修复：检查队列状态时，确保不受已取消任务的影响
    const hasActiveTask = currentTask.value && currentTask.value.status === 'processing';

    // 🔧 修复：检查前后端状态一致性
    const backendStatus = imageState.serviceStatus.value;
    const frontendIsGenerating = isGenerating.value;
    const backendIsGenerating = backendStatus?.isGenerating || false;

    console.log('🔍 [队列检查] 当前状态:', {
      前端isGenerating: frontendIsGenerating,
      后端isGenerating: backendIsGenerating,
      currentTask: currentTask.value?.status,
      hasActiveTask,
      状态一致: frontendIsGenerating === backendIsGenerating,
      rowIndex: rowIndex
    });

    // 🔧 关键修复：基于实际状态决定是否排队
    const shouldQueue = frontendIsGenerating || backendIsGenerating || hasActiveTask;
    
    console.log('🔍 [队列决策] 是否需要排队:', {
      shouldQueue,
      frontendIsGenerating,
      backendIsGenerating,
      hasActiveTask,
      isCancelling: isCancelling.value,
      rowIndex
    });

    // 🔧 关键修复：如果需要排队，立即处理
    if (shouldQueue && !isCancelling.value) {
      console.log('📋 [队列管理] 有任务正在执行，将新任务加入队列');

      const queueTask = addToQueue({
        prompt,
        negativePrompt,
        projectTitle,
        chapterTitle,
        rowIndex,
        onImageGenerated,
        onProgress
      });

      // 🔧 关键修复：通知UI任务已进入队列
      if (onProgress) {
        onProgress({
          stage: 'queued',
          progress: 0,
          message: `排队中 (${queueTask.queuePosition})`,
          isQueued: true,
          taskId: queueTask.id,
          queuePosition: queueTask.queuePosition,
          isProcessing: false,
          isCompleted: false,
          isError: false,
          isCancelled: false
        });
      }

      // 返回队列任务信息而不是直接执行
      return {
        isQueued: true,
        taskId: queueTask.id,
        queuePosition: queueTask.queuePosition
      };
    }

    // 🔧 关键修复：如果没有任务在执行，立即设置状态并执行
    console.log('🔧 [并发保护] 立即设置isGenerating状态，防止并发任务');
    setIsGenerating(true, 'generateImageForRow-开始执行');

    // 🎨 直接执行图像生成
    console.log('🎨 [图像生成] 直接执行图像生成');

    try {
      // 🔧 增强的进度回调，确保状态正确传递
      const enhancedOnProgress = (progressData) => {
        if (onProgress) {
          onProgress({
            ...progressData,
            rowIndex,
            isProcessing: !progressData.isCompleted && !progressData.isError,
            isCompleted: progressData.isCompleted || false,
            isError: progressData.isError || false
          });
        }
      };

      const result = await executeImageGeneration({
        prompt,
        negativePrompt,
        projectTitle,
        chapterTitle,
        rowIndex,
        onImageGenerated,
        onProgress: enhancedOnProgress
      });

      // 🔧 关键修复：执行成功后确保状态重置
      console.log('✅ [图像生成] 直接执行完成，重置状态');
      setIsGenerating(false, 'generateImageForRow-执行成功');

      // 🔧 触发最终完成回调
      if (onProgress) {
        onProgress({
          stage: 'completed',
          progress: 100,
          message: '图像生成完成',
          rowIndex,
          isProcessing: false,
          isCompleted: true,
          isError: false,
          isCancelled: false
        });
      }

      return result;
    } catch (error) {
      // 🔧 关键修复：执行失败后确保状态重置
      console.error('❌ [图像生成] 直接执行失败，重置状态:', error);
      setIsGenerating(false, 'generateImageForRow-执行失败');

      // 🔧 触发错误完成回调
      if (onProgress) {
        onProgress({
          stage: 'error',
          progress: 0,
          message: error.message || '生成失败',
          rowIndex,
          isProcessing: false,
          isCompleted: true,
          isError: true,
          isCancelled: false
        });
      }

      throw error;
    }
  };

  /**
   * 🆕 健壮的同步取消生成 - 确保ComfyUI真正停止
   */
  const cancelGenerationWithCleanup = async () => {
    try {
      console.log('🎨 [图像生成] 开始取消生成...');

      // 🔧 关键修复：立即设置取消状态，防止UI显示问题
      isCancelling.value = true;
      setIsGenerating(false, '取消生成-立即重置');

      // 取消当前生成
      const cancelResult = await cancelGeneration();

      // 清空队列
      await clearQueueAndStop();

      // 重置状态
      resetGenerationState();
      setIsGenerating(false, '取消生成完成');

      console.log('✅ [图像生成] 取消生成完成:', cancelResult);

      // 🔧 返回标准化的取消结果
      return {
        success: true,
        interruptSent: true,
        queueCleared: true,
        verificationPassed: true,
        details: '取消操作已完成'
      };

    } catch (error) {
      console.error('❌ [图像生成] 取消生成失败:', error);
      handleError(error, '取消生成');

      // 🔧 即使出错也要确保状态重置
      setIsGenerating(false, '取消生成-错误重置');
      resetGenerationState();

      // 🔧 返回失败结果
      return {
        success: false,
        interruptSent: false,
        queueCleared: false,
        verificationPassed: false,
        details: error.message || '取消操作失败'
      };
    } finally {
      // 🔧 确保取消状态被重置
      isCancelling.value = false;

      // 🔧 延迟确认状态重置
      setTimeout(() => {
        if (isGenerating.value) {
          console.log('🔧 [延迟修复] 检测到isGenerating未重置，强制重置');
          setIsGenerating(false, '取消生成-延迟重置');
        }
      }, 100);
    }
  };

  /**
   * 获取生成统计信息
   */
  const getGenerationStats = () => {
    const totalGenerations = generationHistory.size;
    const recentGenerations = Array.from(generationHistory.values())
      .flat()
      .filter(entry => {
        const entryTime = new Date(entry.timestamp);
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return entryTime > oneDayAgo;
      });

    return {
      total: totalGenerations,
      recent24h: recentGenerations.length,
      queue: {
        ...queueStats,
        currentTask: currentTask.value
      },
      service: {
        isAvailable: isServiceAvailable.value,
        isGenerating: isGenerating.value,
        isCancelling: isCancelling.value
      }
    };
  };

  // 初始化
  onMounted(async () => {
    console.log('🎨 [图像生成] 组件挂载，初始化服务...');
    await initializeService();
  });

  return {
    // 状态
    isGenerating,
    isCancelling,
    generationProgress,
    generationStage,
    generationMessage,
    generationError,
    currentTaskId,
    isServiceAvailable,
    canGenerate,
    serviceStatus,

    // 队列状态
    taskQueue,
    currentTask,
    queueStats,

    // 核心功能
    generateImageForRow,
    generateImagesForRows,
    cancelGeneration: cancelGenerationWithCleanup,

    // 队列管理
    getRowQueueStatus,
    cancelQueueByRowIndex: (rowIndex) => {
      const result = cancelQueueByRowIndex(rowIndex);
      // 🔧 强制更新队列统计
      imageQueue.updateQueueStats();
      console.log('🔧 [队列同步] 强制更新队列统计:', imageQueue.queueStats);
      return result;
    },
    cancelQueuedTask: (rowIndex) => {
      const result = cancelQueueByRowIndex(rowIndex);
      // 🔧 强制更新队列统计
      imageQueue.updateQueueStats();
      console.log('🔧 [队列同步] 强制更新队列统计:', imageQueue.queueStats);
      return result;
    },
    clearQueue: clearQueueAndStop,

    // 状态管理
    resetGenerationState,
    forceResetState,
    completeStateReset,

    // 历史记录
    getRowGenerationHistory,
    clearGenerationHistory,

    // 统计信息
    getGenerationStats,
    updateQueueStats: imageQueue.updateQueueStats,

    // 服务状态
    getStatus: () => comfyuiImageGeneration.getStatus(),

    // 错误恢复
    errorRecovery
  };
}
