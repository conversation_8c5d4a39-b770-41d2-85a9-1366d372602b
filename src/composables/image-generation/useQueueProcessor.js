/**
 * 队列处理器
 * 负责队列任务的调度和执行
 */

export function useQueueProcessor(imageQueue, imageState, imageExecution) {
  const { 
    taskQueue, 
    currentTask, 
    getNextTask, 
    updateTaskStatus, 
    removeFromQueue,
    clearQueue 
  } = imageQueue;
  
  const { 
    isGenerating, 
    isCancelling, 
    setIsGenerating 
  } = imageState;
  
  const { 
    executeImageGenerationForQueue 
  } = imageExecution;

  /**
   * 统一的队列触发检查函数
   */
  const checkAndTriggerQueue = () => {
    // 延迟检查，确保状态已完全更新
    setTimeout(() => {
      console.log('🔧 [队列处理] 检查队列触发条件:', {
        isGenerating: isGenerating.value,
        isCancelling: isCancelling.value,
        currentTask: currentTask.value?.id,
        queueLength: taskQueue.length,
        hasWaitingTasks: taskQueue.some(t => t.status === 'waiting')
      });

      // 如果没有任务在执行且队列中有等待的任务，则处理队列
      if (!isGenerating.value && 
          !isCancelling.value && 
          !currentTask.value && 
          taskQueue.some(t => t.status === 'waiting')) {
        
        console.log('🚀 [队列处理] 触发队列处理');
        processQueue();
      } else {
        console.log('⏸️ [队列处理] 队列处理条件不满足，跳过');
      }
    }, 100);
  };

  /**
   * 处理队列 - FIFO调度
   */
  const processQueue = async () => {
    console.log('🔧 [队列处理] 开始处理队列，当前状态:', {
      isGenerating: isGenerating.value,
      isCancelling: isCancelling.value,
      currentTask: currentTask.value?.id,
      queueLength: taskQueue.length
    });

    // 检查是否可以处理队列
    if (isGenerating.value || isCancelling.value || currentTask.value) {
      console.log('⏸️ [队列处理] 有任务正在执行或取消中，跳过队列处理');
      return;
    }

    // 获取下一个待处理任务
    const nextTask = getNextTask();
    if (!nextTask) {
      console.log('📭 [队列处理] 队列为空，无任务需要处理');
      return;
    }

    console.log('🎯 [队列处理] 开始处理任务:', {
      taskId: nextTask.id,
      rowIndex: nextTask.rowIndex,
      queuePosition: nextTask.queuePosition
    });

    try {
      // 设置当前任务和状态
      currentTask.value = nextTask;
      setIsGenerating(true, '队列处理开始');
      updateTaskStatus(nextTask.id, 'processing');

      // 🔧 增强的进度回调，确保任务完成时触发状态重置
      const enhancedOnProgress = (progressData) => {
        if (nextTask.onProgress) {
          nextTask.onProgress({
            ...progressData,
            taskId: nextTask.id,
            rowIndex: nextTask.rowIndex
          });
        }

        // 🔧 关键修复：检测任务完成状态
        if (progressData.isCompleted || progressData.stage === 'completed') {
          console.log('🔧 [队列处理] 检测到任务完成信号:', {
            taskId: nextTask.id,
            rowIndex: nextTask.rowIndex,
            stage: progressData.stage,
            isCompleted: progressData.isCompleted
          });
        }
      };

      // 执行任务
      const result = await executeImageGenerationForQueue({
        ...nextTask,
        onProgress: enhancedOnProgress
      });

      // 任务完成
      console.log('✅ [队列处理] 任务执行成功:', {
        taskId: nextTask.id,
        rowIndex: nextTask.rowIndex,
        result: result.success
      });

      updateTaskStatus(nextTask.id, 'completed', { result });

      // 🔧 关键修复：触发最终完成回调
      if (nextTask.onProgress) {
        nextTask.onProgress({
          stage: 'completed',
          progress: 100,
          message: '队列任务完成',
          taskId: nextTask.id,
          rowIndex: nextTask.rowIndex,
          isProcessing: false,
          isCompleted: true,
          isError: false,
          isCancelled: false,
          isQueued: false
        });
      }

    } catch (error) {
      // 任务失败
      console.error('❌ [队列处理] 任务执行失败:', {
        taskId: nextTask.id,
        rowIndex: nextTask.rowIndex,
        error: error.message
      });

      updateTaskStatus(nextTask.id, 'failed', { error: error.message });

      // 🔧 关键修复：触发错误完成回调
      if (nextTask.onProgress) {
        nextTask.onProgress({
          stage: 'error',
          progress: 0,
          message: error.message || '队列任务失败',
          taskId: nextTask.id,
          rowIndex: nextTask.rowIndex,
          isProcessing: false,
          isCompleted: true,
          isError: true,
          isCancelled: false,
          isQueued: false
        });
      }

    } finally {
      // 🔧 关键修复：确保状态完全清理
      console.log('🔧 [队列处理] 开始清理任务状态:', {
        taskId: nextTask.id,
        rowIndex: nextTask.rowIndex
      });

      // 清理状态
      currentTask.value = null;
      setIsGenerating(false, '队列处理完成');

      // 移除已完成的任务
      removeFromQueue(nextTask.id);

      console.log('🔧 [队列处理] 任务状态清理完成:', {
        taskId: nextTask.id,
        currentTask: currentTask.value,
        isGenerating: isGenerating.value,
        remainingQueueLength: taskQueue.length
      });

      // 继续处理下一个任务
      console.log('🔄 [队列处理] 任务处理完成，检查下一个任务...');
      checkAndTriggerQueue();
    }
  };

  /**
   * 清空队列并停止处理
   */
  const clearQueueAndStop = async () => {
    console.log('🛑 [队列处理] 开始清空队列并停止处理...');

    try {
      // 如果有正在执行的任务，先取消
      if (currentTask.value && isGenerating.value) {
        console.log('🚫 [队列处理] 取消当前执行的任务:', currentTask.value.id);
        
        isCancelling.value = true;
        
        try {
          await imageExecution.cancelGeneration();
        } catch (error) {
          console.warn('⚠️ [队列处理] 取消当前任务失败:', error);
        }
        
        // 更新当前任务状态
        if (currentTask.value) {
          updateTaskStatus(currentTask.value.id, 'cancelled');
          currentTask.value = null;
        }
        
        isCancelling.value = false;
        setIsGenerating(false, '队列清空-取消当前任务');
      }

      // 清空队列
      await clearQueue();

      console.log('✅ [队列处理] 队列已清空并停止处理');

    } catch (error) {
      console.error('❌ [队列处理] 清空队列失败:', error);
      throw error;
    }
  };

  /**
   * 暂停队列处理
   */
  const pauseQueue = () => {
    console.log('⏸️ [队列处理] 暂停队列处理');
    // 通过设置标志位来暂停，具体实现可以根据需要扩展
  };

  /**
   * 恢复队列处理
   */
  const resumeQueue = () => {
    console.log('▶️ [队列处理] 恢复队列处理');
    checkAndTriggerQueue();
  };

  /**
   * 获取队列处理状态
   */
  const getProcessorStatus = () => {
    return {
      isProcessing: isGenerating.value,
      isCancelling: isCancelling.value,
      currentTask: currentTask.value,
      queueLength: taskQueue.length,
      waitingTasks: taskQueue.filter(t => t.status === 'waiting').length,
      processingTasks: taskQueue.filter(t => t.status === 'processing').length,
      canProcess: !isGenerating.value && !isCancelling.value && !currentTask.value
    };
  };

  /**
   * 重新处理失败的任务
   */
  const retryFailedTasks = () => {
    const failedTasks = taskQueue.filter(t => t.status === 'failed');
    
    console.log('🔄 [队列处理] 重新处理失败任务:', {
      count: failedTasks.length
    });

    failedTasks.forEach(task => {
      updateTaskStatus(task.id, 'waiting');
    });

    if (failedTasks.length > 0) {
      checkAndTriggerQueue();
    }
  };

  /**
   * 优先处理指定任务
   */
  const prioritizeTask = (taskId) => {
    const taskIndex = taskQueue.findIndex(t => t.id === taskId);
    if (taskIndex > 0) {
      // 将任务移到队列前面
      const task = taskQueue.splice(taskIndex, 1)[0];
      taskQueue.unshift(task);
      
      // 更新队列位置
      taskQueue.forEach((t, index) => {
        if (t.status === 'waiting') {
          t.queuePosition = index + 1;
        }
      });

      console.log('⬆️ [队列处理] 任务已优先处理:', {
        taskId,
        newPosition: 1
      });

      checkAndTriggerQueue();
    }
  };

  return {
    // 核心处理方法
    processQueue,
    checkAndTriggerQueue,

    // 控制方法
    clearQueueAndStop,
    pauseQueue,
    resumeQueue,
    retryFailedTasks,
    prioritizeTask,

    // 状态查询
    getProcessorStatus
  };
}
