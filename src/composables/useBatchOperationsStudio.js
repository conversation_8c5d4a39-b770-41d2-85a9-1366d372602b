/**
 * 批量操作管理 Composable for ContentCreationStudio
 * 从 ContentCreationStudio.vue 中提取的批量操作协调和管理功能
 * 
 * 功能：
 * - 批量推理操作协调
 * - 批量图像生成操作协调
 * - 批量取消操作管理
 * - 队列管理操作
 * - 批量操作状态同步
 */

import { reactive } from 'vue';

export function useBatchOperationsStudio() {
  // 批量操作状态
  const batchState = reactive({
    isAnyBatchProcessing: false,
    currentOperation: null,
    lastOperationResult: null
  });

  /**
   * 批量推理操作 - 顺序处理版本
   * @param {Object} context - 组件上下文
   */
  const performBatchInference = async (context) => {
    console.log('🎯 [useBatchOperationsStudio] performBatchInference 被调用');
    console.log('🧠 [批量推理管理] 开始顺序批量推理操作');

    // 检查是否正在处理中，如果是则执行取消操作
    if (context.batchInferenceSequential.isProcessing.value) {
      console.log('🧠 [批量推理管理] 检测到正在处理中，执行取消操作');
      await performBatchInferenceCancel(context);
      return;
    }

    // 🔧 修复选中行数据获取
    console.log('🔍 [批量推理管理] 检查context中的选中行数据:', {
      'context.selectedRows': context.selectedRows?.length || 0,
      'context.rows': context.rows?.filter(r => r.isSelected)?.length || 0,
      'context.mergedRows': context.mergedRows?.filter(r => r.isSelected)?.length || 0
    });

    // 尝试多种方式获取选中行
    let actualSelectedRows = context.selectedRows;
    if (!actualSelectedRows || actualSelectedRows.length === 0) {
      // 尝试从rows中获取选中的行
      actualSelectedRows = context.rows?.filter(row => row.isSelected) || [];
    }
    if (!actualSelectedRows || actualSelectedRows.length === 0) {
      // 尝试从mergedRows中获取选中的行
      actualSelectedRows = context.mergedRows?.filter(row => row.isSelected) || [];
    }

    console.log('🔍 [批量推理管理] 最终获取的选中行数量:', actualSelectedRows.length);

    // 验证用户选择
    const validation = context.batchOperationUI.validateUserSelection(
      actualSelectedRows,
      context.showToast,
      'inference'
    );

    if (!validation.isValid) {
      return;
    }

    const selectedRows = validation.validRows;
    batchState.currentOperation = 'inference';

    try {
      // 启动批量推理UI状态
      console.log('🧠 [批量推理管理] 启动UI状态，选中行数量:', selectedRows.length);
      context.batchOperationUI.startBatchInference({
        taskInfo: { totalRows: selectedRows.length },
        onStart: () => {
          console.log('🧠 [批量推理管理] UI状态启动回调执行');
          context.showInfoMessage('批量推理', `开始为 ${selectedRows.length} 行生成图像提示词...`);
          syncButtonStates(context); // 同步按钮状态

          // 🔧 强制触发UI更新
          console.log('🔧 [批量推理管理] 强制触发UI更新');
          context.$forceUpdate && context.$forceUpdate();
        }
      });

      // 定义推理函数 - 复用现有的 handleInferPrompt
      const inferenceFunction = async (row, index) => {
        console.log(`🧠 [批量推理管理] 处理第 ${index + 1} 行:`, row.description?.substring(0, 100));

        // 找到该行在原始rows数组中的索引
        const rowIndex = context.rows.findIndex(r => r === row);
        if (rowIndex === -1) {
          throw new Error(`无法找到行索引`);
        }

        console.log(`🧠 [批量推理管理] 调用现有推理功能，行索引: ${rowIndex}`);

        // 调用现有的推理提示词功能，传递批量模式参数
        const result = await context.handleInferPrompt(rowIndex, true);

        console.log(`🧠 [批量推理管理] 第 ${index + 1} 行推理完成:`, result);
        return result;
      };

      // 定义进度回调
      const onProgress = (progressInfo) => {
        console.log(`🧠 [批量推理管理] 进度更新: ${progressInfo.current}/${progressInfo.total}`);
        // 可以在这里添加进度显示逻辑
      };

      // 定义单行完成回调
      const onRowComplete = (row, index, result, error) => {
        if (error) {
          console.error(`🧠 [批量推理管理] 第 ${index + 1} 行推理失败:`, error);
        } else {
          console.log(`🧠 [批量推理管理] 第 ${index + 1} 行推理成功`);
        }

        // 自动取消选中已完成的行
        row.isSelected = false;
      };

      // 执行顺序批量推理
      const result = await context.batchInferenceSequential.startBatchInference(
        selectedRows,
        inferenceFunction,
        onProgress,
        onRowComplete
      );

      // 完成批量推理UI状态
      context.batchOperationUI.completeBatchInference({
        onComplete: () => {
          const successCount = result.processed - result.failed;
          syncButtonStates(context); // 同步按钮状态
          context.showSuccessMessage(
            '批量推理完成',
            `成功为 ${successCount}/${result.total} 行生成图像提示词`
          );
        }
      });

      // 保存项目数据
      context.debouncedSaveProject();
      batchState.lastOperationResult = result;

    } catch (error) {
      console.error('🧠 [批量推理管理] 批量推理失败:', error);

      // 完成批量推理UI状态（错误情况）
      context.batchOperationUI.completeBatchInference({ error: true });
      syncButtonStates(context); // 同步按钮状态

      context.showErrorMessage('批量推理失败', error.message);
    } finally {
      batchState.currentOperation = null;
    }
  };

  /**
   * 批量推理取消操作
   * @param {Object} context - 组件上下文
   */
  const performBatchInferenceCancel = async (context) => {
    console.log('🚫 [批量推理管理] 开始取消批量推理操作');

    try {
      // 取消批量推理
      context.batchInferenceSequential.cancelBatchInference();

      // 等待当前任务完成
      let attempts = 0;
      const maxAttempts = 30; // 最多等待30秒

      while (context.batchInferenceSequential.isProcessing.value && attempts < maxAttempts) {
        console.log(`🚫 [批量推理管理] 等待当前任务完成... (${attempts + 1}/${maxAttempts})`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        attempts++;
      }

      // 强制完成UI状态
      context.batchOperationUI.completeBatchInference({ cancelled: true });
      syncButtonStates(context);

      // 自动取消选中所有行
      context.selectedRows.forEach(row => {
        row.isSelected = false;
      });

      context.showInfoMessage('批量推理已取消', '当前正在处理的任务已完成，其余任务已取消');

    } catch (error) {
      console.error('🚫 [批量推理管理] 批量取消失败:', error);
      context.showErrorMessage('批量推理取消失败', error.message);
    }
  };

  /**
   * 批量图像生成操作 - 修复版本
   * @param {Object} context - 组件上下文
   */
  const performBatchImageGeneration = async (context) => {
    console.log('🎨 [批量生图管理] 开始批量图像生成操作');

    // 检查是否正在处理中，如果是则执行批量取消操作
    if (context.batchOperationUI.isBatchImageGenerationProcessing.value) {
      console.log('🎨 [批量生图管理] 检测到正在处理中，执行批量取消操作');
      await performBatchCancel(context);
      return;
    }

    // 验证选中的行
    const validation = context.batchOperationUI.validateUserSelection(
      context.selectedRows,
      context.showToast,
      'generation'
    );

    if (!validation.isValid) {
      return;
    }

    const rowsWithKeywords = validation.validRows;
    batchState.currentOperation = 'generation';

    try {
      // 🔧 修复：启动批量图像生成UI状态，确保状态正确同步
      context.batchOperationUI.startBatchImageGeneration({
        taskInfo: { totalRows: rowsWithKeywords.length },
        onStart: () => {
          context.showInfoMessage('批量生图', `开始为 ${rowsWithKeywords.length} 行生成图像...`);

          // 🔧 强制UI更新和状态同步
          context.$nextTick(() => {
            syncButtonStates(context);
            context.$forceUpdate && context.$forceUpdate();
          });
        }
      });

      // 🔧 修复：改为顺序处理而非并行，避免队列混乱
      let queuedCount = 0;
      let failedCount = 0;
      const results = [];

      console.log(`🎨 [批量生图管理] 开始顺序处理 ${rowsWithKeywords.length} 行`);

      // 🔧 修复：顺序处理每一行，确保状态管理正确，添加延迟避免竞态条件
      for (let i = 0; i < rowsWithKeywords.length; i++) {
        const row = rowsWithKeywords[i];

        try {
          // 找到该行在原始rows数组中的索引
          const rowIndex = context.rows.findIndex(r => r === row);
          if (rowIndex === -1) {
            throw new Error(`无法找到行索引`);
          }

          console.log(`🎨 [批量生图管理] 处理第 ${i + 1}/${rowsWithKeywords.length} 行 (索引: ${rowIndex + 1})`);

          // 🔧 修复：添加小延迟，确保前一行的状态已经正确设置
          if (i > 0) {
            console.log(`🔧 [批量生图管理] 等待 ${i} 行状态稳定...`);
            await new Promise(resolve => setTimeout(resolve, 100));
          }

          // 🔧 修复：添加进度回调来正确处理状态
          const result = await context.generateImageForRow(rowIndex);

          console.log(`🎨 [批量生图管理] 第 ${rowIndex + 1} 行处理完成:`, result);

          // 🔧 修复：只有在成功添加到队列时才取消选中
          if (result && (result.isQueued || result.success)) {
            row.isSelected = false;
            queuedCount++;
          }

          results.push({ success: true, rowIndex, result });

        } catch (error) {
          console.error(`🎨 [批量生图管理] 第 ${i + 1} 行处理失败:`, error);
          failedCount++;
          results.push({ success: false, rowIndex: i, error: error.message });
        }

        // 🔧 修复：每处理一行后更新UI状态并等待状态稳定
        await context.$nextTick();
        syncButtonStates(context);

        // 🔧 添加额外的延迟确保UI更新完成
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // 🔧 修复：完成批量图像生成UI状态，确保状态正确重置
      context.batchOperationUI.completeBatchImageGeneration({
        onComplete: () => {
          // 🔧 强制UI更新
          context.$nextTick(() => {
            syncButtonStates(context);
            context.$forceUpdate && context.$forceUpdate();

            // 显示完成消息
            context.showSuccessMessage(
              '批量生图完成',
              `成功为 ${queuedCount} 行添加到生成队列，${failedCount} 行失败`
            );
          });
        }
      });

      // 保存项目数据
      context.debouncedSaveProject();
      batchState.lastOperationResult = { queuedCount, failedCount, results };

    } catch (error) {
      console.error('🎨 [批量生图管理] 批量图像生成失败:', error);

      // 🔧 修复：错误情况下的状态重置
      context.batchOperationUI.completeBatchImageGeneration({ error: true });

      context.$nextTick(() => {
        syncButtonStates(context);
        context.$forceUpdate && context.$forceUpdate();
      });

      context.showErrorMessage('批量生图失败', error.message);
    } finally {
      batchState.currentOperation = null;

      // 🔧 修复：确保最终状态重置
      context.$nextTick(() => {
        syncButtonStates(context);
      });
    }
  };

  /**
   * 批量取消操作（主要针对图像生成）- 修复版本
   * @param {Object} context - 组件上下文
   */
  const performBatchCancel = async (context) => {
    console.log('🚫 [批量操作管理] 开始批量取消操作');

    try {
      // 🔧 修复：取消所有排队和生成中的任务
      const cancelledTasks = [];
      const cancelledGenerating = [];

      // 🔧 修复：遍历所有行，取消排队和生成状态
      context.rows.forEach((row, index) => {
        // 取消排队任务
        if (row.isQueued) {
          console.log(`🚫 [批量操作管理] 取消第 ${index + 1} 行的排队任务`);

          // 调用图像生成服务的取消排队方法
          if (row.queueTaskId) {
            context.imageGeneration.cancelQueuedTask(row.queueTaskId);
          }

          // 清除排队状态
          row.isQueued = false;
          row.queueTaskId = '';
          row.queuePosition = 0;
          row.generationMessage = '';

          // 自动取消选中
          row.isSelected = false;

          cancelledTasks.push(index + 1);
        }

        // 🔧 修复：取消生成中的任务
        if (row.isGenerating) {
          console.log(`🚫 [批量操作管理] 取消第 ${index + 1} 行的生成任务`);

          // 调用取消生成方法
          if (context.handleCancelGeneration) {
            context.handleCancelGeneration(index);
          }

          // 清除生成状态
          row.isGenerating = false;
          row.generationMessage = '';

          // 自动取消选中
          row.isSelected = false;

          cancelledGenerating.push(index + 1);
        }
      });

      // 🔧 修复：完成批量图像生成UI状态，确保状态正确重置
      context.batchOperationUI.completeBatchImageGeneration({ cancelled: true });

      // 🔧 强制UI更新
      context.$nextTick(() => {
        syncButtonStates(context);
        context.$forceUpdate && context.$forceUpdate();
      });

      // 🔧 修复：显示更详细的取消信息
      const totalCancelled = cancelledTasks.length + cancelledGenerating.length;
      if (totalCancelled > 0) {
        let message = `已取消 ${totalCancelled} 个任务`;
        if (cancelledTasks.length > 0) {
          message += `（排队: ${cancelledTasks.length}`;
        }
        if (cancelledGenerating.length > 0) {
          message += cancelledTasks.length > 0 ? `，生成中: ${cancelledGenerating.length}` : `（生成中: ${cancelledGenerating.length}`;
        }
        message += '）';

        context.showInfoMessage('批量取消完成', message);
      } else {
        context.showInfoMessage('批量取消', '没有找到需要取消的任务');
      }

    } catch (error) {
      console.error('🚫 [批量操作管理] 批量取消失败:', error);
      context.showErrorMessage('批量取消失败', error.message);
    }
  };

  /**
   * 处理清空队列
   * @param {Object} context - 组件上下文
   */
  const handleClearQueue = async (context) => {
    console.log('📋 [队列管理] 清空队列');

    try {
      // 调用图像生成服务的清空队列方法
      context.imageGeneration.clearQueue();

      // 清除所有行的队列状态
      context.rows.forEach(row => {
        if (row.isQueued) {
          row.isQueued = false;
          row.queueTaskId = '';
          row.queuePosition = 0;
          row.generationMessage = '';
        }
      });

      console.log('✅ [队列管理] 队列已清空');

    } catch (error) {
      console.error('❌ [队列管理] 清空队列时出错:', error);
      context.showErrorMessage('清空失败', error.message || '清空队列时发生错误');
    }
  };

  /**
   * 处理取消排队
   * @param {Object} context - 组件上下文
   * @param {number} rowIndex - 行索引
   */
  const handleCancelQueue = async (context, rowIndex) => {
    console.log('📋 [队列管理] 取消排队，行索引:', rowIndex);

    try {
      const row = context.rows[rowIndex];
      if (!row || !row.isQueued) {
        console.warn('📋 [队列管理] 该行未在队列中');
        return;
      }

      // 🔧 修复：调用图像生成服务的取消排队方法，传入行索引而不是任务ID
      console.log('📋 [队列管理] 调用cancelQueuedTask，参数:', { rowIndex, queueTaskId: row.queueTaskId });
      context.imageGeneration.cancelQueuedTask(rowIndex);

      // 清除该行的队列状态
      row.isQueued = false;
      row.queueTaskId = '';
      row.queuePosition = 0;
      row.generationMessage = '';

      console.log('✅ [队列管理] 已取消排队');

    } catch (error) {
      console.error('❌ [队列管理] 取消排队时出错:', error);
      context.showErrorMessage('取消失败', error.message || '取消排队时发生错误');
    }
  };

  /**
   * 切换队列面板折叠状态
   * @param {Object} context - 组件上下文
   */
  const toggleQueuePanel = (context) => {
    context.isQueuePanelCollapsed = !context.isQueuePanelCollapsed;
    console.log('📋 [队列面板] 切换折叠状态:', context.isQueuePanelCollapsed ? '折叠' : '展开');
  };

  /**
   * 同步按钮状态
   * @param {Object} context - 组件上下文
   */
  const syncButtonStates = (context) => {
    // 更新批量操作状态
    batchState.isAnyBatchProcessing =
      context.batchOperationUI.isAnyBatchProcessing.value;

    console.log('🔄 [批量操作管理] 按钮状态已同步:', {
      isAnyBatchProcessing: batchState.isAnyBatchProcessing,
      currentOperation: batchState.currentOperation
    });
  };

  return {
    // 状态
    batchState,

    // 批量操作方法
    performBatchInference,
    performBatchInferenceCancel,
    performBatchImageGeneration,
    performBatchCancel,

    // 队列管理方法
    handleClearQueue,
    handleCancelQueue,
    toggleQueuePanel,

    // 工具方法
    syncButtonStates
  };
}
